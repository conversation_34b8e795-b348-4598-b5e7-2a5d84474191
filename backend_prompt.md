# Desenvolvimento do Backend - Assistência de Drones

## Contexto do Projeto
Estamos desenvolvendo um sistema de assistência de drones com:
- Backend: Rust + Axum (desenvolvimento nesta aba)
- Frontend: Rust + Leptos (desenvolvimento em outra aba)
- Banco de dados: PostgreSQL (já configurado e rodando)

## Estado Atual
- ✅ Estrutura do projeto criada
- ✅ Dependências configuradas
- ✅ Docker com PostgreSQL rodando
- ✅ Workspace Rust configurado

## Tarefa: Desenvolvimento do Backend

### 1. Estrutura de Arquivos
```
backend/
└── src/
    ├── models/
    │   ├── user.rs
    │   ├── drone.rs
    │   └── maintenance.rs
    ├── handlers/
    │   ├── auth.rs
    │   ├── drones.rs
    │   └── maintenance.rs
    ├── routes/
    │   ├── auth.rs
    │   ├── drones.rs
    │   └── maintenance.rs
    └── services/
        ├── auth.rs
        ├── drones.rs
        └── maintenance.rs
```

### 2. Dependências (já configuradas)
```toml
[package]
name = "backend"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "^0.7"
tokio = { version = "^1.32", features = ["full"] }
sqlx = { version = "^0.7", features = ["postgres", "runtime-tokio-native-tls"] }
validator = "^0.16"
tracing = "^0.1"
serde = { version = "^1.0", features = ["derive"] }
serde_json = "^1.0"
uuid = { version = "^1.4", features = ["serde", "v4"] }
dotenv = "^0.15"
```

### 3. Tarefas Prioritárias

#### 3.1 Modelos de Dados
1. Implementar modelo User:
   - id: UUID
   - email: String
   - password_hash: String
   - name: String
   - created_at: DateTime
   - updated_at: DateTime

2. Implementar modelo Drone:
   - id: UUID
   - name: String
   - model: String
   - status: Enum
   - last_maintenance: DateTime
   - owner_id: UUID (FK)
   - created_at: DateTime
   - updated_at: DateTime

3. Implementar modelo Maintenance:
   - id: UUID
   - drone_id: UUID (FK)
   - technician_id: UUID (FK)
   - description: String
   - status: Enum
   - scheduled_date: DateTime
   - completed_date: DateTime
   - created_at: DateTime
   - updated_at: DateTime

#### 3.2 Banco de Dados
1. Criar migrations:
   - users
   - drones
   - maintenance

2. Implementar conexão:
   - Configurar pool de conexões
   - Implementar repositórios

#### 3.3 Serviços
1. AuthService:
   - register
   - login
   - validate_token

2. DroneService:
   - create
   - update
   - delete
   - list
   - get_by_id

3. MaintenanceService:
   - schedule
   - update_status
   - list
   - get_by_id

#### 3.4 Rotas da API
1. Auth Routes:
   - POST /auth/register
   - POST /auth/login
   - GET /auth/me

2. Drone Routes:
   - GET /drones
   - POST /drones
   - GET /drones/:id
   - PUT /drones/:id
   - DELETE /drones/:id

3. Maintenance Routes:
   - GET /maintenance
   - POST /maintenance
   - GET /maintenance/:id
   - PUT /maintenance/:id
   - GET /drones/:id/maintenance

### 4. Interface com Frontend
O frontend espera as seguintes respostas:
- Formato: JSON
- Status codes padrão HTTP
- Respostas de erro padronizadas

### 5. Comandos Úteis
```bash
# Navegar até o diretório do backend
cd /root/windsurf-project/assistencia_drones_rust_app/backend

# Compilar o backend
cargo build

# Executar o backend
cargo run

# Executar migrations
sqlx migrate run
```

### 6. Notas Importantes
- Implementar validação de dados
- Tratar erros adequadamente
- Implementar logging
- Seguir princípios de segurança

### 7. Próximos Passos
1. Implementar modelos de dados
2. Criar e executar migrations
3. Implementar serviços
4. Criar rotas da API

## Recursos
- Documentação do Axum: https://docs.rs/axum
- Documentação do SQLx: https://docs.rs/sqlx
- Documentação do Serde: https://docs.rs/serde 