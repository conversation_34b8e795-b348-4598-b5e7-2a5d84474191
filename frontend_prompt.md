# Desenvolvimento do Frontend - Assistência de Drones

## Contexto do Projeto
Estamos desenvolvendo um sistema de assistência de drones com:
- Backend: Rust + Axum (em desenvolvimento em outra aba)
- Frontend: Rust + Leptos
- Banco de dados: PostgreSQL (já configurado e rodando)

## Estado Atual
- ✅ Estrutura do projeto criada
- ✅ Dependências configuradas
- ✅ Docker com PostgreSQL rodando
- ✅ Workspace Rust configurado

## Tarefa: Desenvolvimento do Frontend

### 1. Estrutura de Arquivos
```
frontend/
└── src/
    ├── components/
    │   ├── auth/
    │   ├── drones/
    │   └── maintenance/
    ├── pages/
    │   ├── login.rs
    │   ├── register.rs
    │   └── dashboard.rs
    └── services/
```

### 2. Dependências (já configuradas)
```toml
[package]
name = "frontend"
version = "0.1.0"
edition = "2021"

[dependencies]
leptos = "^0.4"
reqwasm = "^0.1"
serde = { version = "^1.0", features = ["derive"] }
serde_json = "^1.0"
```

### 3. Tarefas Prioritárias

#### 3.1 Componentes Básicos
1. Criar componentes de autenticação:
   - LoginForm
   - RegisterForm
   - AuthLayout

2. Criar componentes de drones:
   - DroneList
   - DroneCard
   - DroneForm

3. Criar componentes de manutenção:
   - MaintenanceList
   - MaintenanceCard
   - MaintenanceForm

#### 3.2 Páginas
1. Implementar página de login
2. Implementar página de registro
3. Implementar dashboard principal

#### 3.3 Serviços
1. Configurar cliente HTTP
2. Implementar serviços de API:
   - AuthService
   - DroneService
   - MaintenanceService

### 4. Interface com Backend
O backend estará disponível em:
- URL: http://localhost:3000
- Endpoints principais:
  - /auth/login
  - /auth/register
  - /drones
  - /maintenance

### 5. Estilo e UI
- Usar CSS moderno
- Implementar design responsivo
- Seguir princípios de UX

### 6. Comandos Úteis
```bash
# Navegar até o diretório do frontend
cd /root/windsurf-project/assistencia_drones_rust_app/frontend

# Compilar o frontend
cargo build

# Executar o frontend
cargo run
```

### 7. Notas Importantes
- Manter consistência com o design do sistema
- Implementar validações de formulários
- Tratar erros adequadamente
- Implementar feedback visual para ações do usuário

### 8. Próximos Passos
1. Começar pelos componentes básicos
2. Implementar páginas principais
3. Integrar com o backend
4. Adicionar estilos e melhorias de UX

## Recursos
- Documentação do Leptos: https://docs.rs/leptos
- Documentação do Reqwasm: https://docs.rs/reqwasm
- Documentação do Serde: https://docs.rs/serde 