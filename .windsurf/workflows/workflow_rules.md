# Workflow Rules - Assistência Drones Rust App

## 1. Estrutura Básica
```
tasks/
  ├── backend/
  │   ├── auth/
  │   ├── api/
  │   └── db/
  ├── frontend/
  │   ├── ui/
  │   └── components/
  └── shared/
      └── types/
```

## 2. Regras de Arquivos
- Nome: task_X.md (onde X é o número sequencial)
- Extensão: .md
- Tags obrigatórias: [type], [priority], [status]
- Status: pending, in_progress, completed, blocked, cancelled

## 3. Comandos Automáticos
```bash
# Verifica status atual
verify_task()

# Reinicia verificação
restart_check()

# Direciona para próxima tarefa
next_task()

# Valida regras
dvalidate_rules()
```

## 4. Métricas e Logs
- Tempo de execução por tarefa
- Número de tentativas
- Erros encontrados
- Recursos utilizados

## 5. Alertas
- Tarefa bloqueada
- Timeout
- Recursos insuficientes
- Erros críticos
