import numpy as np

class Drone:
    def __init__(self):
        # Physical parameters
        self.mass = 0.5  # kg
        self.gravity = 9.81  # m/s^2
        
        # Initial state
        self.position = np.array([0.0, 0.0, 0.0])  # x, y, z
        self.velocity = np.array([0.0, 0.0, 0.0])
        self.acceleration = np.array([0.0, 0.0, 0.0])
        
        # Rotational state
        self.orientation = np.array([0.0, 0.0, 0.0])  # roll, pitch, yaw
        self.angular_velocity = np.array([0.0, 0.0, 0.0])
        
    def update_state(self, forces, torques, dt):
        """Update drone state based on forces and torques"""
        # Update linear motion
        self.acceleration = forces / self.mass
        self.velocity += self.acceleration * dt
        self.position += self.velocity * dt
        
        # Update rotational motion (simplified)
        self.angular_velocity += torques * dt
        self.orientation += self.angular_velocity * dt
        
    def get_state(self):
        """Return current state of the drone"""
        return {
            'position': self.position,
            'velocity': self.velocity,
            'orientation': self.orientation
        }
