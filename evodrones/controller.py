import numpy as np

class Controller:
    def __init__(self):
        # PID gains (example values)
        self.kp = np.array([1.0, 1.0, 1.0])  # Proportional gains
        self.ki = np.array([0.1, 0.1, 0.1])  # Integral gains
        self.kd = np.array([0.5, 0.5, 0.5])  # Derivative gains
        
        # Integral error
        self.integral_error = np.zeros(3)
        
    def compute_control(self, target, current_state, dt):
        """Compute control forces based on target position"""
        # Extract position
        current_pos = current_state['position']
        
        # Compute errors
        error = target - current_pos
        self.integral_error += error * dt
        derivative_error = (error - self.previous_error) / dt
        
        # Store current error for next iteration
        self.previous_error = error
        
        # Compute control forces using PID
        control_forces = (
            self.kp * error +
            self.ki * self.integral_error +
            self.kd * derivative_error
        )
        
        return control_forces
