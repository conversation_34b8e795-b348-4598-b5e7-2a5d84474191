import numpy as np
import matplotlib.pyplot as plt
from drone import Drone
from controller import Controller

class Simulation:
    def __init__(self, drone, controller, duration=10.0, dt=0.01):
        self.drone = drone
        self.controller = controller
        self.duration = duration
        self.dt = dt
        
        # Store simulation data
        self.time = []
        self.positions = []
        self.velocities = []
        
    def run(self):
        """Run the simulation"""
        t = 0
        target = np.array([5.0, 5.0, 5.0])  # Target position
        
        while t < self.duration:
            # Get current state
            current_state = self.drone.get_state()
            
            # Compute control forces
            control_forces = self.controller.compute_control(target, current_state, self.dt)
            
            # Update drone state
            self.drone.update_state(control_forces, np.zeros(3), self.dt)
            
            # Store data
            self.time.append(t)
            self.positions.append(self.drone.position)
            self.velocities.append(self.drone.velocity)
            
            t += self.dt
            
    def plot_results(self):
        """Plot simulation results"""
        plt.figure(figsize=(12, 6))
        
        # Plot position
        plt.subplot(2, 1, 1)
        positions = np.array(self.positions)
        plt.plot(self.time, positions[:, 0], label='x')
        plt.plot(self.time, positions[:, 1], label='y')
        plt.plot(self.time, positions[:, 2], label='z')
        plt.xlabel('Time (s)')
        plt.ylabel('Position (m)')
        plt.legend()
        plt.grid(True)
        
        # Plot velocity
        plt.subplot(2, 1, 2)
        velocities = np.array(self.velocities)
        plt.plot(self.time, velocities[:, 0], label='vx')
        plt.plot(self.time, velocities[:, 1], label='vy')
        plt.plot(self.time, velocities[:, 2], label='vz')
        plt.xlabel('Time (s)')
        plt.ylabel('Velocity (m/s)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
