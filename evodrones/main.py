from drone import Drone
from simulation import Simulation
from controller import Controller
import matplotlib.pyplot as plt
import numpy as np

def main():
    # Initialize drone
    drone = Drone()
    
    # Initialize controller
    controller = Controller()
    
    # Initialize simulation
    sim = Simulation(drone, controller)
    
    # Run simulation
    sim.run()
    
    # Plot results
    sim.plot_results()

if __name__ == "__main__":
    main()
