use serde::{Serialize, Deserialize};
use validator::<PERSON><PERSON><PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON><PERSON>)]
pub struct Drone {
    pub id: i32,
    pub model: String,
    pub manufacturer: String,
    pub status: DroneStatus,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct NewDrone {
    #[validate(length(min = 3, max = 100))]
    pub model: String,
    
    #[validate(length(min = 3, max = 100))]
    pub manufacturer: String,
    
    pub status: DroneStatus,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum DroneStatus {
    Available,
    InMaintenance,
    InUse,
    Retired,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct MaintenanceLog {
    pub id: i32,
    pub drone_id: i32,
    pub description: String,
    pub status: MaintenanceStatus,
    pub created_at: chrono::NaiveDateTime,
    pub completed_at: Option<chrono::NaiveDateTime>,
}

#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON>ialize, Deserialize, Validate)]
pub struct NewMaintenanceLog {
    pub drone_id: i32,
    
    #[validate(length(min = 10, max = 1000))]
    pub description: String,
    
    pub status: MaintenanceStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MaintenanceStatus {
    Pending,
    InProgress,
    Completed,
    Cancelled,
}
