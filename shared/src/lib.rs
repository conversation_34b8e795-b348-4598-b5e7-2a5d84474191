pub mod models;
pub mod schema;
pub mod error;

use serde::{Serialize, Deserialize};
use validator::Valida<PERSON>;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Validate)]
pub struct User {
    pub id: i32,
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 3, max = 50))]
    pub name: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Valida<PERSON>)]
pub struct NewUser {
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 3, max = 50))]
    pub name: String,
    #[validate(length(min = 8))]
    pub password: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub exp: usize,
    pub iat: usize,
}
