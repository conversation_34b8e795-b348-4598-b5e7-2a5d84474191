[package]
name = "assistencia-drones-shared"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
validator = { version = "0.16", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
jsonwebtoken = "9.0"
sqlx = { version = "0.7", features = ["postgres", "chrono", "runtime-tokio-native-tls"], optional = true }
axum = { version = "0.7", optional = true }
