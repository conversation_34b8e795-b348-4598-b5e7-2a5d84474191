-- Create drone_status enum type
CREATE TYPE drone_status AS ENUM ('active', 'inactive', 'maintenance', 'retired');

-- Create drones table
CREATE TABLE IF NOT EXISTS drones (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    status drone_status NOT NULL DEFAULT 'active',
    last_maintenance TIMESTAMP WITH TIME ZONE,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS drones_owner_id_idx ON drones(owner_id);
CREATE INDEX IF NOT EXISTS drones_status_idx ON drones(status); 