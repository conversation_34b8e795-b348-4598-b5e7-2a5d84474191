-- Create maintenance_status enum type
CREATE TYPE maintenance_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');

-- Create maintenance table
CREATE TABLE IF NOT EXISTS maintenance (
    id UUID PRIMARY KEY,
    drone_id UUID NOT NULL REFERENCES drones(id) ON DELETE CASCADE,
    technician_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    status maintenance_status NOT NULL DEFAULT 'scheduled',
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS maintenance_drone_id_idx ON maintenance(drone_id);
CREATE INDEX IF NOT EXISTS maintenance_technician_id_idx ON maintenance(technician_id);
CREATE INDEX IF NOT EXISTS maintenance_status_idx ON maintenance(status);
CREATE INDEX IF NOT EXISTS maintenance_scheduled_date_idx ON maintenance(scheduled_date); 