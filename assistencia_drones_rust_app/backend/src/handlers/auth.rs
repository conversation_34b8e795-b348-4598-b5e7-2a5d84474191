use axum::{
    extract::State,
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde_json::json;
use uuid::Uuid;

use crate::{
    models::{CreateUser, LoginUser},
    services::AuthService,
};

pub async fn register(
    State(auth_service): State<AuthService>,
    <PERSON><PERSON>(user_data): <PERSON><PERSON><CreateUser>,
) -> impl IntoResponse {
    match auth_service.register(user_data).await {
        Ok(user) => (
            StatusCode::CREATED,
            Json(json!({
                "message": "Usuário registrado com sucesso",
                "user": user
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao registrar usuário",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn login(
    State(auth_service): State<AuthService>,
    <PERSON><PERSON>(login_data): <PERSON><PERSON><LoginUser>,
) -> impl IntoResponse {
    match auth_service.login(login_data).await {
        Ok(auth_response) => (
            StatusCode::OK,
            <PERSON>son(json!({
                "message": "Login realizado com sucesso",
                "token": auth_response.token,
                "user": auth_response.user
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::UNAUTHORIZED,
            Json(json!({
                "error": "Credenciais inválidas",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
} 