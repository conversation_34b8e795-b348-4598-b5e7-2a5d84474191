use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde_json::json;
use uuid::Uuid;

use crate::{
    models::{CreateMaintenance, UpdateMaintenance},
    services::MaintenanceService,
};

pub async fn create_maintenance(
    State(maintenance_service): State<MaintenanceService>,
    Json(maintenance_data): Json<CreateMaintenance>,
) -> impl IntoResponse {
    match maintenance_service.create(maintenance_data).await {
        Ok(maintenance) => (
            StatusCode::CREATED,
            Json(json!({
                "message": "Manutenção agendada com sucesso",
                "maintenance": maintenance
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao agendar manutenção",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn update_maintenance(
    State(maintenance_service): State<MaintenanceService>,
    Path(id): Path<Uuid>,
    <PERSON>son(maintenance_data): J<PERSON><UpdateMaintenance>,
) -> impl IntoResponse {
    match maintenance_service.update(id, maintenance_data).await {
        Ok(maintenance) => (
            StatusCode::OK,
            Json(json!({
                "message": "Manutenção atualizada com sucesso",
                "maintenance": maintenance
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao atualizar manutenção",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn delete_maintenance(
    State(maintenance_service): State<MaintenanceService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match maintenance_service.delete(id).await {
        Ok(_) => (
            StatusCode::OK,
            Json(json!({
                "message": "Manutenção excluída com sucesso"
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao excluir manutenção",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn get_maintenance(
    State(maintenance_service): State<MaintenanceService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match maintenance_service.get_by_id(id).await {
        Ok(Some(maintenance)) => (
            StatusCode::OK,
            Json(json!({
                "maintenance": maintenance
            })),
        )
            .into_response(),
        Ok(None) => (
            StatusCode::NOT_FOUND,
            Json(json!({
                "error": "Manutenção não encontrada"
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao buscar manutenção",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn list_maintenance_by_drone(
    State(maintenance_service): State<MaintenanceService>,
    Path(drone_id): Path<Uuid>,
) -> impl IntoResponse {
    match maintenance_service.list_by_drone(drone_id).await {
        Ok(maintenance_records) => (
            StatusCode::OK,
            Json(json!({
                "maintenance_records": maintenance_records
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao listar manutenções",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn list_maintenance_by_technician(
    State(maintenance_service): State<MaintenanceService>,
    Path(technician_id): Path<Uuid>,
) -> impl IntoResponse {
    match maintenance_service.list_by_technician(technician_id).await {
        Ok(maintenance_records) => (
            StatusCode::OK,
            Json(json!({
                "maintenance_records": maintenance_records
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao listar manutenções",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn list_all_maintenance(
    State(maintenance_service): State<MaintenanceService>,
) -> impl IntoResponse {
    match maintenance_service.list_all().await {
        Ok(maintenance_records) => (
            StatusCode::OK,
            Json(json!({
                "maintenance_records": maintenance_records
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao listar manutenções",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
} 