use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde_json::json;
use uuid::Uuid;

use crate::{
    models::{CreateDrone, UpdateDrone},
    services::DroneService,
};

pub async fn create_drone(
    State(drone_service): State<DroneService>,
    Json(drone_data): J<PERSON><CreateDrone>,
) -> impl IntoResponse {
    match drone_service.create(drone_data).await {
        Ok(drone) => (
            StatusCode::CREATED,
            Json(json!({
                "message": "Drone criado com sucesso",
                "drone": drone
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao criar drone",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn update_drone(
    State(drone_service): State<DroneService>,
    Path(id): Path<Uuid>,
    <PERSON><PERSON>(drone_data): <PERSON><PERSON><UpdateDrone>,
) -> impl IntoResponse {
    match drone_service.update(id, drone_data).await {
        Ok(drone) => (
            StatusCode::OK,
            Json(json!({
                "message": "Drone atualizado com sucesso",
                "drone": drone
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao atualizar drone",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn delete_drone(
    State(drone_service): State<DroneService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match drone_service.delete(id).await {
        Ok(_) => (
            StatusCode::OK,
            Json(json!({
                "message": "Drone excluído com sucesso"
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao excluir drone",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn get_drone(
    State(drone_service): State<DroneService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match drone_service.get_by_id(id).await {
        Ok(Some(drone)) => (
            StatusCode::OK,
            Json(json!({
                "drone": drone
            })),
        )
            .into_response(),
        Ok(None) => (
            StatusCode::NOT_FOUND,
            Json(json!({
                "error": "Drone não encontrado"
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao buscar drone",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn list_drones_by_owner(
    State(drone_service): State<DroneService>,
    Path(owner_id): Path<Uuid>,
) -> impl IntoResponse {
    match drone_service.list_by_owner(owner_id).await {
        Ok(drones) => (
            StatusCode::OK,
            Json(json!({
                "drones": drones
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao listar drones",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
}

pub async fn list_all_drones(
    State(drone_service): State<DroneService>,
) -> impl IntoResponse {
    match drone_service.list_all().await {
        Ok(drones) => (
            StatusCode::OK,
            Json(json!({
                "drones": drones
            })),
        )
            .into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(json!({
                "error": "Erro ao listar drones",
                "details": e.to_string()
            })),
        )
            .into_response(),
    }
} 