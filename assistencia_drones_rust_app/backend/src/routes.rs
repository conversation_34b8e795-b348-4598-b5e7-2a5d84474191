use axum::{
    routing::{delete, get, post, put},
    Router,
};

use crate::{
    handlers::{
        auth::{login, register},
        drone::{
            create_drone, delete_drone, get_drone, list_all_drones, list_drones_by_owner,
            update_drone,
        },
        maintenance::{
            create_maintenance, delete_maintenance, get_maintenance, list_all_maintenance,
            list_maintenance_by_drone, list_maintenance_by_technician, update_maintenance,
        },
    },
    middleware::auth_middleware,
    services::{AuthService, DroneService, MaintenanceService},
};

pub fn create_router(
    auth_service: AuthService,
    drone_service: DroneService,
    maintenance_service: MaintenanceService,
) -> Router {
    let auth_router = Router::new()
        .route("/register", post(register))
        .route("/login", post(login))
        .with_state(auth_service.clone());

    let drone_router = Router::new()
        .route("/", post(create_drone))
        .route("/", get(list_all_drones))
        .route("/:id", get(get_drone))
        .route("/:id", put(update_drone))
        .route("/:id", delete(delete_drone))
        .route("/owner/:owner_id", get(list_drones_by_owner))
        .with_state(drone_service)
        .route_layer(axum::middleware::from_fn_with_state(
            auth_service.clone(),
            auth_middleware,
        ));

    let maintenance_router = Router::new()
        .route("/", post(create_maintenance))
        .route("/", get(list_all_maintenance))
        .route("/:id", get(get_maintenance))
        .route("/:id", put(update_maintenance))
        .route("/:id", delete(delete_maintenance))
        .route("/drone/:drone_id", get(list_maintenance_by_drone))
        .route("/technician/:technician_id", get(list_maintenance_by_technician))
        .with_state(maintenance_service)
        .route_layer(axum::middleware::from_fn_with_state(
            auth_service,
            auth_middleware,
        ));

    Router::new()
        .nest("/auth", auth_router)
        .nest("/drones", drone_router)
        .nest("/maintenance", maintenance_router)
} 