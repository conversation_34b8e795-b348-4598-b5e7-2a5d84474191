use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, FromRow, Validate)]
pub struct Maintenance {
    pub id: Uuid,
    pub drone_id: Uuid,
    pub technician_id: Uuid,
    #[validate(length(min = 10, max = 1000))]
    pub description: String,
    pub status: MaintenanceStatus,
    pub scheduled_date: DateTime<Utc>,
    pub completed_date: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, sqlx::Type, PartialEq, Eq, Clone, Copy)]
#[sqlx(type_name = "maintenance_status", rename_all = "snake_case")]
pub enum MaintenanceStatus {
    Scheduled,
    InProgress,
    Completed,
    Cancelled,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateMaintenance {
    pub drone_id: Uuid,
    pub technician_id: Uuid,
    #[validate(length(min = 10, max = 1000))]
    pub description: String,
    pub scheduled_date: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateMaintenance {
    #[validate(length(min = 10, max = 1000))]
    pub description: Option<String>,
    pub status: Option<MaintenanceStatus>,
    pub scheduled_date: Option<DateTime<Utc>>,
    pub completed_date: Option<DateTime<Utc>>,
}

impl Maintenance {
    pub fn new(
        id: Uuid,
        drone_id: Uuid,
        technician_id: Uuid,
        description: String,
        scheduled_date: DateTime<Utc>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            drone_id,
            technician_id,
            description,
            status: MaintenanceStatus::Scheduled,
            scheduled_date,
            completed_date: None,
            created_at: now,
            updated_at: now,
        }
    }
}
