use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, FromRow, Validate)]
pub struct Drone {
    pub id: Uuid,
    #[validate(length(min = 2, max = 100))]
    pub name: String,
    #[validate(length(min = 2, max = 100))]
    pub model: String,
    pub status: DroneStatus,
    pub last_maintenance: Option<DateTime<Utc>>,
    pub owner_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, sqlx::Type, PartialEq, Eq, Clone, Copy)]
#[sqlx(type_name = "drone_status", rename_all = "snake_case")]
pub enum DroneStatus {
    Active,
    Inactive,
    Maintenance,
    Retired,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateDrone {
    #[validate(length(min = 2, max = 100))]
    pub name: String,
    #[validate(length(min = 2, max = 100))]
    pub model: String,
    pub status: DroneStatus,
    pub owner_id: Uuid,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateDrone {
    #[validate(length(min = 2, max = 100))]
    pub name: Option<String>,
    #[validate(length(min = 2, max = 100))]
    pub model: Option<String>,
    pub status: Option<DroneStatus>,
    pub last_maintenance: Option<DateTime<Utc>>,
}

impl Drone {
    pub fn new(
        id: Uuid,
        name: String,
        model: String,
        status: DroneStatus,
        owner_id: Uuid,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            name,
            model,
            status,
            last_maintenance: None,
            owner_id,
            created_at: now,
            updated_at: now,
        }
    }
}
