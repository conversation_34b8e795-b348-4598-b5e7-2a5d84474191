use axum::{
    extract::Request,
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use serde_json::json;

use crate::services::AuthService;

pub async fn auth_middleware(
    State(auth_service): State<AuthService>,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_header = request
        .headers()
        .get("Authorization")
        .and_then(|value| value.to_str().ok())
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                json!({
                    "error": "Token de autenticação não fornecido"
                })
                .to_string(),
            )
        })?;

    let token = auth_header
        .strip_prefix("Bearer ")
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                json!({
                    "error": "Formato de token inválido"
                })
                .to_string(),
            )
        })?;

    match auth_service.validate_token(token) {
        Ok(_) => Ok(next.run(request).await),
        Err(_) => Err((
            StatusCode::UNAUTHORIZED,
            json!({
                "error": "Token inválido ou expirado"
            })
            .to_string(),
        )),
    }
} 