// src/main.rs
mod handlers;
mod middleware;
mod models;
mod routes;
mod services;

use axum::Server;
use sqlx::postgres::PgPoolOptions;
use std::net::SocketAddr;
use dotenv::dotenv;
use tracing_subscriber;

#[tokio::main]
async fn main() {
    // Carrega as variáveis de ambiente do arquivo .env
    dotenv::dotenv().ok();

    // Inicializa o logger
    tracing_subscriber::fmt::init();

    // Conecta ao banco de dados
    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await
        .expect("Failed to connect to database");

    // Inicializa os serviços
    let auth_service = services::AuthService::new(pool.clone());
    let drone_service = services::DroneService::new(pool.clone());
    let maintenance_service = services::MaintenanceService::new(pool);

    // Cria o router com as rotas
    let app = routes::create_router(auth_service, drone_service, maintenance_service);

    // Configura o endereço do servidor
    let addr = SocketAddr::from(([127, 0, 0, 1], 3000));
    tracing::info!("Servidor rodando em {}", addr);

    // Inicia o servidor
    Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}
