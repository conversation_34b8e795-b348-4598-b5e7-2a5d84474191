use sqlx::PgPool;
use uuid::Uuid;

use crate::models::{CreateDrone, Drone, UpdateDrone};

pub struct DroneService {
    pool: PgPool,
}

impl DroneService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create(&self, drone_data: CreateDrone) -> Result<Drone, sqlx::Error> {
        let drone = Drone::new(
            Uuid::new_v4(),
            drone_data.name,
            drone_data.model,
            drone_data.status,
            drone_data.owner_id,
        );

        sqlx::query!(
            r#"
            INSERT INTO drones (id, name, model, status, owner_id)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, name, model, status, last_maintenance, owner_id, created_at, updated_at
            "#,
            drone.id,
            drone.name,
            drone.model,
            drone.status as _,
            drone.owner_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(drone)
    }

    pub async fn update(&self, id: Uuid, drone_data: UpdateDrone) -> Result<Drone, sqlx::Error> {
        let drone = sqlx::query_as!(
            Drone,
            r#"
            UPDATE drones
            SET 
                name = COALESCE($1, name),
                model = COALESCE($2, model),
                status = COALESCE($3, status),
                last_maintenance = COALESCE($4, last_maintenance),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $5
            RETURNING id, name, model, status, last_maintenance, owner_id, created_at, updated_at
            "#,
            drone_data.name,
            drone_data.model,
            drone_data.status as _,
            drone_data.last_maintenance,
            id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(drone)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            DELETE FROM drones
            WHERE id = $1
            "#,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Drone>, sqlx::Error> {
        let drone = sqlx::query_as!(
            Drone,
            r#"
            SELECT id, name, model, status, last_maintenance, owner_id, created_at, updated_at
            FROM drones
            WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(drone)
    }

    pub async fn list_by_owner(&self, owner_id: Uuid) -> Result<Vec<Drone>, sqlx::Error> {
        let drones = sqlx::query_as!(
            Drone,
            r#"
            SELECT id, name, model, status, last_maintenance, owner_id, created_at, updated_at
            FROM drones
            WHERE owner_id = $1
            ORDER BY created_at DESC
            "#,
            owner_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(drones)
    }

    pub async fn list_all(&self) -> Result<Vec<Drone>, sqlx::Error> {
        let drones = sqlx::query_as!(
            Drone,
            r#"
            SELECT id, name, model, status, last_maintenance, owner_id, created_at, updated_at
            FROM drones
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(drones)
    }
}
