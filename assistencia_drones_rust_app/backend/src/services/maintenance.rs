use sqlx::PgPool;
use uuid::Uuid;

use crate::models::{CreateMaintenance, Maintenance, UpdateMaintenance};

pub struct MaintenanceService {
    pool: PgPool,
}

impl MaintenanceService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create(&self, maintenance_data: CreateMaintenance) -> Result<Maintenance, sqlx::Error> {
        let maintenance = Maintenance::new(
            Uuid::new_v4(),
            maintenance_data.drone_id,
            maintenance_data.technician_id,
            maintenance_data.description,
            maintenance_data.status,
            maintenance_data.scheduled_date,
        );

        sqlx::query!(
            r#"
            INSERT INTO maintenance (id, drone_id, technician_id, description, status, scheduled_date)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            "#,
            maintenance.id,
            maintenance.drone_id,
            maintenance.technician_id,
            maintenance.description,
            maintenance.status as _,
            maintenance.scheduled_date
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(maintenance)
    }

    pub async fn update(&self, id: Uuid, maintenance_data: UpdateMaintenance) -> Result<Maintenance, sqlx::Error> {
        let maintenance = sqlx::query_as!(
            Maintenance,
            r#"
            UPDATE maintenance
            SET 
                description = COALESCE($1, description),
                status = COALESCE($2, status),
                scheduled_date = COALESCE($3, scheduled_date),
                completed_date = COALESCE($4, completed_date),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $5
            RETURNING id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            "#,
            maintenance_data.description,
            maintenance_data.status as _,
            maintenance_data.scheduled_date,
            maintenance_data.completed_date,
            id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(maintenance)
    }

    pub async fn delete(&self, id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            DELETE FROM maintenance
            WHERE id = $1
            "#,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Maintenance>, sqlx::Error> {
        let maintenance = sqlx::query_as!(
            Maintenance,
            r#"
            SELECT id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            FROM maintenance
            WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(maintenance)
    }

    pub async fn list_by_drone(&self, drone_id: Uuid) -> Result<Vec<Maintenance>, sqlx::Error> {
        let maintenance_records = sqlx::query_as!(
            Maintenance,
            r#"
            SELECT id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            FROM maintenance
            WHERE drone_id = $1
            ORDER BY scheduled_date DESC
            "#,
            drone_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(maintenance_records)
    }

    pub async fn list_by_technician(&self, technician_id: Uuid) -> Result<Vec<Maintenance>, sqlx::Error> {
        let maintenance_records = sqlx::query_as!(
            Maintenance,
            r#"
            SELECT id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            FROM maintenance
            WHERE technician_id = $1
            ORDER BY scheduled_date DESC
            "#,
            technician_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(maintenance_records)
    }

    pub async fn list_all(&self) -> Result<Vec<Maintenance>, sqlx::Error> {
        let maintenance_records = sqlx::query_as!(
            Maintenance,
            r#"
            SELECT id, drone_id, technician_id, description, status, scheduled_date, completed_date, created_at, updated_at
            FROM maintenance
            ORDER BY scheduled_date DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(maintenance_records)
    }
}
