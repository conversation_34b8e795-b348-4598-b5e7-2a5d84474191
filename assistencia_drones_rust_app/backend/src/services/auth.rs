use argon2::{
    password_hash::{rand_core::OsRng, Password<PERSON>ash, PasswordHasher, PasswordVerifier, SaltString},
    Argon2,
};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Decod<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use uuid::Uuid;

use crate::models::{CreateUser, LoginUser, User};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // user id
    pub exp: i64,    // expiration time
}

#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub token: String,
    pub user: User,
}

pub struct AuthService {
    pool: PgPool,
    jwt_secret: String,
}

impl AuthService {
    pub fn new(pool: PgPool) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET").expect("JWT_SECRET must be set");
        Self { pool, jwt_secret }
    }

    pub async fn register(&self, user_data: CreateUser) -> Result<User, sqlx::Error> {
        let password_hash = self.hash_password(&user_data.password)?;
        let user = User::new(
            Uuid::new_v4(),
            user_data.email,
            password_hash,
            user_data.name,
        );

        sqlx::query!(
            r#"
            INSERT INTO users (id, email, password_hash, name)
            VALUES ($1, $2, $3, $4)
            RETURNING id, email, password_hash, name, created_at, updated_at
            "#,
            user.id,
            user.email,
            user.password_hash,
            user.name
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn login(&self, login_data: LoginUser) -> Result<AuthResponse, anyhow::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, email, password_hash, name, created_at, updated_at
            FROM users
            WHERE email = $1
            "#,
            login_data.email
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Invalid credentials"))?;

        self.verify_password(&login_data.password, &user.password_hash)?;

        let token = self.generate_token(user.id)?;

        Ok(AuthResponse { token, user })
    }

    pub fn validate_token(&self, token: &str) -> Result<Uuid, jsonwebtoken::errors::Error> {
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_bytes()),
            &Validation::default(),
        )?;

        Ok(Uuid::parse_str(&token_data.claims.sub).unwrap())
    }

    fn hash_password(&self, password: &str) -> Result<String, argon2::password_hash::Error> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)?
            .to_string();
        Ok(password_hash)
    }

    fn verify_password(
        &self,
        password: &str,
        hash: &str,
    ) -> Result<(), argon2::password_hash::Error> {
        let parsed_hash = PasswordHash::new(hash)?;
        Argon2::default().verify_password(password.as_bytes(), &parsed_hash)
    }

    fn generate_token(&self, user_id: Uuid) -> Result<String, jsonwebtoken::errors::Error> {
        let expiration = Utc::now()
            .checked_add_signed(Duration::hours(24))
            .expect("valid timestamp")
            .timestamp();

        let claims = Claims {
            sub: user_id.to_string(),
            exp: expiration,
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_bytes()),
        )
    }
}
