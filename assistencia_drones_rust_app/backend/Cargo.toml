[package]
name = "backend"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "^0.7"
tokio = { version = "^1.32", features = ["full"] }
sqlx = { version = "^0.7", features = ["postgres", "runtime-tokio-native-tls"] }
validator = "^0.16"
tracing = "^0.1"
serde = { version = "^1.0", features = ["derive"] }
serde_json = "^1.0"
uuid = { version = "^1.4", features = ["serde", "v4"] }
dotenv = "^0.15"
