# Frontend da Aplicação de Assistência de Drones

Este é o frontend da aplicação de assistência de drones, desenvolvido com React, TypeScript e Tailwind CSS.

## Requisitos

- Node.js 18 ou superior
- npm 9 ou superior

## Instalação

1. Clone o repositório
2. Navegue até o diretório do frontend:
   ```bash
   cd assistencia_drones_rust_app/frontend
   ```
3. Instale as dependências:
   ```bash
   npm install
   ```

## Desenvolvimento

Para iniciar o servidor de desenvolvimento:

```bash
npm run dev
```

O servidor será iniciado em `http://localhost:5173`.

## Build

Para criar uma build de produção:

```bash
npm run build
```

Os arquivos serão gerados no diretório `dist`.

## Preview

Para visualizar a build de produção localmente:

```bash
npm run preview
```

## Estrutura do Projeto

```
src/
  ├── components/     # Componentes reutilizáveis
  ├── pages/         # Páginas da aplicação
  ├── services/      # Serviços de API
  ├── App.tsx        # Componente principal
  ├── main.tsx       # Ponto de entrada
  ├── routes.tsx     # Configuração de rotas
  └── index.css      # Estilos globais
```

## Tecnologias Utilizadas

- React 18
- TypeScript
- React Router DOM
- Axios
- Tailwind CSS
- Vite 