import api from './api';

export interface Maintenance {
    id: string;
    drone_id: string;
    technician_id: string;
    description: string;
    status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
    scheduled_date: string;
    completed_date: string | null;
    created_at: string;
    updated_at: string;
}

export interface CreateMaintenanceData {
    drone_id: string;
    technician_id: string;
    description: string;
    status: Maintenance['status'];
    scheduled_date: string;
}

export interface UpdateMaintenanceData {
    description?: string;
    status?: Maintenance['status'];
    scheduled_date?: string;
    completed_date?: string | null;
}

export const maintenanceService = {
    async create(data: CreateMaintenanceData): Promise<Maintenance> {
        const response = await api.post<{ maintenance: Maintenance }>('/maintenance', data);
        return response.data.maintenance;
    },

    async update(id: string, data: UpdateMaintenanceData): Promise<Maintenance> {
        const response = await api.put<{ maintenance: Maintenance }>(`/maintenance/${id}`, data);
        return response.data.maintenance;
    },

    async delete(id: string): Promise<void> {
        await api.delete(`/maintenance/${id}`);
    },

    async getById(id: string): Promise<Maintenance> {
        const response = await api.get<{ maintenance: Maintenance }>(`/maintenance/${id}`);
        return response.data.maintenance;
    },

    async listByDrone(droneId: string): Promise<Maintenance[]> {
        const response = await api.get<{ maintenance_records: Maintenance[] }>(`/maintenance/drone/${droneId}`);
        return response.data.maintenance_records;
    },

    async listByTechnician(technicianId: string): Promise<Maintenance[]> {
        const response = await api.get<{ maintenance_records: Maintenance[] }>(`/maintenance/technician/${technicianId}`);
        return response.data.maintenance_records;
    },

    async listAll(): Promise<Maintenance[]> {
        const response = await api.get<{ maintenance_records: Maintenance[] }>('/maintenance');
        return response.data.maintenance_records;
    },
}; 