import api from './api';

export interface Drone {
    id: string;
    name: string;
    model: string;
    status: 'active' | 'inactive' | 'maintenance' | 'retired';
    last_maintenance: string | null;
    owner_id: string;
    created_at: string;
    updated_at: string;
}

export interface CreateDroneData {
    name: string;
    model: string;
    status: Drone['status'];
    owner_id: string;
}

export interface UpdateDroneData {
    name?: string;
    model?: string;
    status?: Drone['status'];
    last_maintenance?: string | null;
}

export const droneService = {
    async create(data: CreateDroneData): Promise<Drone> {
        const response = await api.post<{ drone: Drone }>('/drones', data);
        return response.data.drone;
    },

    async update(id: string, data: UpdateDroneData): Promise<Drone> {
        const response = await api.put<{ drone: Drone }>(`/drones/${id}`, data);
        return response.data.drone;
    },

    async delete(id: string): Promise<void> {
        await api.delete(`/drones/${id}`);
    },

    async getById(id: string): Promise<Drone> {
        const response = await api.get<{ drone: Drone }>(`/drones/${id}`);
        return response.data.drone;
    },

    async listByOwner(ownerId: string): Promise<Drone[]> {
        const response = await api.get<{ drones: Drone[] }>(`/drones/owner/${ownerId}`);
        return response.data.drones;
    },

    async listAll(): Promise<Drone[]> {
        const response = await api.get<{ drones: Drone[] }>('/drones');
        return response.data.drones;
    },
}; 