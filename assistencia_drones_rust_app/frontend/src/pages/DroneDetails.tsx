import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON>ton, Card } from '../components';
import { droneService, maintenanceService, Drone, Maintenance } from '../services';

export const DroneDetails: React.FC = () => {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [drone, setDrone] = useState<Drone | null>(null);
    const [maintenances, setMaintenances] = useState<Maintenance[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        if (id) {
            loadDrone(id);
            loadMaintenances(id);
        }
    }, [id]);

    const loadDrone = async (droneId: string) => {
        try {
            const data = await droneService.getById(droneId);
            setDrone(data);
        } catch (err) {
            setError('Erro ao carregar dados do drone');
        }
    };

    const loadMaintenances = async (droneId: string) => {
        try {
            const data = await maintenanceService.listByDrone(droneId);
            setMaintenances(data);
        } catch (err) {
            setError('Erro ao carregar manutenções');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async () => {
        if (!drone || !window.confirm('Tem certeza que deseja excluir este drone?')) {
            return;
        }

        try {
            await droneService.delete(drone.id);
            navigate('/dashboard');
        } catch (err) {
            setError('Erro ao excluir drone');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <p>Carregando...</p>
            </div>
        );
    }

    if (!drone) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <p>Drone não encontrado</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            <nav className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="secondary"
                                onClick={() => navigate('/dashboard')}
                            >
                                Voltar
                            </Button>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="secondary"
                                onClick={() => navigate(`/drones/${drone.id}/edit`)}
                            >
                                Editar
                            </Button>
                            <Button
                                variant="danger"
                                onClick={handleDelete}
                            >
                                Excluir
                            </Button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    {error && (
                        <p className="text-red-600 mb-4">{error}</p>
                    )}

                    <Card className="mb-8">
                        <h2 className="text-2xl font-bold mb-4">{drone.name}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-600">Modelo: {drone.model}</p>
                                <p className="text-gray-600">Status: {drone.status}</p>
                                <p className="text-gray-600">Data de Registro: {new Date(drone.created_at).toLocaleDateString()}</p>
                            </div>
                            <div>
                                <p className="text-gray-600">Última Atualização: {new Date(drone.updated_at).toLocaleDateString()}</p>
                            </div>
                        </div>
                    </Card>

                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-xl font-bold">Histórico de Manutenções</h3>
                        <Button
                            onClick={() => navigate(`/maintenance/new?droneId=${drone.id}`)}
                        >
                            Nova Manutenção
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                        {maintenances.map((maintenance) => (
                            <Card key={maintenance.id}>
                                <div className="flex justify-between items-start">
                                    <div>
                                        <h4 className="text-lg font-semibold mb-2">
                                            Manutenção #{maintenance.id}
                                        </h4>
                                        <p className="text-gray-600 mb-2">
                                            Status: {maintenance.status}
                                        </p>
                                        <p className="text-gray-600 mb-2">
                                            Data Agendada: {new Date(maintenance.scheduled_date).toLocaleDateString()}
                                        </p>
                                        {maintenance.completed_date && (
                                            <p className="text-gray-600 mb-2">
                                                Data de Conclusão: {new Date(maintenance.completed_date).toLocaleDateString()}
                                            </p>
                                        )}
                                    </div>
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        onClick={() => navigate(`/maintenance/${maintenance.id}`)}
                                    >
                                        Detalhes
                                    </Button>
                                </div>
                            </Card>
                        ))}

                        {maintenances.length === 0 && (
                            <p className="text-center text-gray-600">
                                Nenhuma manutenção registrada.
                            </p>
                        )}
                    </div>
                </div>
            </main>
        </div>
    );
}; 