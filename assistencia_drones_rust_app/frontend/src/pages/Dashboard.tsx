import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card } from '../components';
import { authService, droneService, Drone } from '../services';

export const Dashboard: React.FC = () => {
    const navigate = useNavigate();
    const [drones, setDrones] = useState<Drone[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        const user = authService.getUser();
        if (!user) {
            navigate('/login');
            return;
        }

        loadDrones(user.id);
    }, [navigate]);

    const loadDrones = async (ownerId: string) => {
        try {
            const data = await droneService.listByOwner(ownerId);
            setDrones(data);
        } catch (err) {
            setError('Erro ao carregar drones');
        } finally {
            setLoading(false);
        }
    };

    const handleLogout = () => {
        authService.logout();
        navigate('/login');
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <p>Carregando...</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            <nav className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <h1 className="text-xl font-semibold">Assistência de Drones</h1>
                        </div>
                        <div className="flex items-center">
                            <Button
                                variant="secondary"
                                onClick={handleLogout}
                            >
                                Sair
                            </Button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold">Meus Drones</h2>
                        <Button
                            onClick={() => navigate('/drones/new')}
                        >
                            Novo Drone
                        </Button>
                    </div>

                    {error && (
                        <p className="text-red-600 mb-4">{error}</p>
                    )}

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                        {drones.map((drone) => (
                            <Card key={drone.id}>
                                <h3 className="text-lg font-semibold mb-2">{drone.name}</h3>
                                <p className="text-gray-600 mb-2">Modelo: {drone.model}</p>
                                <p className="text-gray-600 mb-4">Status: {drone.status}</p>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        onClick={() => navigate(`/drones/${drone.id}`)}
                                    >
                                        Detalhes
                                    </Button>
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        onClick={() => navigate(`/maintenance/new?droneId=${drone.id}`)}
                                    >
                                        Agendar Manutenção
                                    </Button>
                                </div>
                            </Card>
                        ))}
                    </div>

                    {drones.length === 0 && !loading && (
                        <p className="text-center text-gray-600">
                            Você ainda não tem drones cadastrados.
                        </p>
                    )}
                </div>
            </main>
        </div>
    );
}; 