import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Button, Card, Input, Select } from '../components';
import { maintenanceService, droneService, Drone } from '../services';

interface MaintenanceFormData {
    drone_id: string;
    description: string;
    status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
    scheduled_date: string;
    completed_date?: string;
}

const initialFormData: MaintenanceFormData = {
    drone_id: '',
    description: '',
    status: 'scheduled',
    scheduled_date: new Date().toISOString().split('T')[0]
};

const statusOptions = [
    { value: 'scheduled', label: 'Agendada' },
    { value: 'in_progress', label: 'Em Andamento' },
    { value: 'completed', label: 'Con<PERSON><PERSON><PERSON><PERSON>' },
    { value: 'cancelled', label: 'Cancelada' }
];

export const MaintenanceForm: React.FC = () => {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [searchParams] = useSearchParams();
    const [formData, setFormData] = useState<MaintenanceFormData>(initialFormData);
    const [drones, setDrones] = useState<Drone[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        loadDrones();
        if (id) {
            loadMaintenance(id);
        } else {
            const droneId = searchParams.get('droneId');
            if (droneId) {
                setFormData(prev => ({ ...prev, drone_id: droneId }));
            }
        }
    }, [id, searchParams]);

    const loadDrones = async () => {
        try {
            const data = await droneService.listAll();
            setDrones(data);
        } catch (err) {
            setError('Erro ao carregar drones');
        }
    };

    const loadMaintenance = async (maintenanceId: string) => {
        try {
            const maintenance = await maintenanceService.getById(maintenanceId);
            setFormData({
                drone_id: maintenance.drone_id,
                description: maintenance.description,
                status: maintenance.status,
                scheduled_date: new Date(maintenance.scheduled_date).toISOString().split('T')[0],
                completed_date: maintenance.completed_date
                    ? new Date(maintenance.completed_date).toISOString().split('T')[0]
                    : undefined
            });
        } catch (err) {
            setError('Erro ao carregar dados da manutenção');
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            if (id) {
                await maintenanceService.update(id, formData);
            } else {
                await maintenanceService.create(formData);
            }

            navigate('/dashboard');
        } catch (err) {
            setError('Erro ao salvar manutenção');
        } finally {
            setLoading(false);
        }
    };

    const droneOptions = drones.map(drone => ({
        value: drone.id,
        label: `${drone.name} (${drone.model})`
    }));

    return (
        <div className="min-h-screen bg-gray-100">
            <nav className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="secondary"
                                onClick={() => navigate('/dashboard')}
                            >
                                Voltar
                            </Button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <Card>
                        <h2 className="text-2xl font-bold mb-6">
                            {id ? 'Editar Manutenção' : 'Nova Manutenção'}
                        </h2>

                        {error && (
                            <p className="text-red-600 mb-4">{error}</p>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="space-y-4">
                                <Select
                                    label="Drone"
                                    name="drone_id"
                                    value={formData.drone_id}
                                    onChange={handleChange}
                                    options={droneOptions}
                                    required
                                />

                                <Input
                                    label="Descrição"
                                    name="description"
                                    value={formData.description}
                                    onChange={handleChange}
                                    required
                                />

                                <Input
                                    label="Data Agendada"
                                    name="scheduled_date"
                                    type="date"
                                    value={formData.scheduled_date}
                                    onChange={handleChange}
                                    required
                                />

                                <Select
                                    label="Status"
                                    name="status"
                                    value={formData.status}
                                    onChange={handleChange}
                                    options={statusOptions}
                                    required
                                />

                                {formData.status === 'completed' && (
                                    <Input
                                        label="Data de Conclusão"
                                        name="completed_date"
                                        type="date"
                                        value={formData.completed_date || ''}
                                        onChange={handleChange}
                                        required
                                    />
                                )}

                                <div className="flex justify-end space-x-4">
                                    <Button
                                        type="button"
                                        variant="secondary"
                                        onClick={() => navigate('/dashboard')}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={loading}
                                    >
                                        {loading ? 'Salvando...' : 'Salvar'}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Card>
                </div>
            </main>
        </div>
    );
}; 