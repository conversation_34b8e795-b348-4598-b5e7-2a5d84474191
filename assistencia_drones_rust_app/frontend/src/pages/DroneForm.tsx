import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button, Card, Input, Select } from '../components';
import { droneService, authService } from '../services';

interface DroneFormData {
    name: string;
    model: string;
    status: 'active' | 'maintenance' | 'inactive';
}

const initialFormData: DroneFormData = {
    name: '',
    model: '',
    status: 'active'
};

const statusOptions = [
    { value: 'active', label: 'Ativo' },
    { value: 'maintenance', label: 'Em Manutenção' },
    { value: 'inactive', label: 'Inativo' }
];

export const DroneForm: React.FC = () => {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [formData, setFormData] = useState<DroneFormData>(initialFormData);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        if (id) {
            loadDrone(id);
        }
    }, [id]);

    const loadDrone = async (droneId: string) => {
        try {
            const drone = await droneService.getById(droneId);
            setFormData({
                name: drone.name,
                model: drone.model,
                status: drone.status
            });
        } catch (err) {
            setError('Erro ao carregar dados do drone');
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const user = authService.getUser();
            if (!user) {
                throw new Error('Usuário não autenticado');
            }

            if (id) {
                await droneService.update(id, formData);
            } else {
                await droneService.create({
                    ...formData,
                    owner_id: user.id
                });
            }

            navigate('/dashboard');
        } catch (err) {
            setError('Erro ao salvar drone');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gray-100">
            <nav className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="secondary"
                                onClick={() => navigate('/dashboard')}
                            >
                                Voltar
                            </Button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <Card>
                        <h2 className="text-2xl font-bold mb-6">
                            {id ? 'Editar Drone' : 'Novo Drone'}
                        </h2>

                        {error && (
                            <p className="text-red-600 mb-4">{error}</p>
                        )}

                        <form onSubmit={handleSubmit}>
                            <div className="space-y-4">
                                <Input
                                    label="Nome"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    required
                                />

                                <Input
                                    label="Modelo"
                                    name="model"
                                    value={formData.model}
                                    onChange={handleChange}
                                    required
                                />

                                <Select
                                    label="Status"
                                    name="status"
                                    value={formData.status}
                                    onChange={handleChange}
                                    options={statusOptions}
                                    required
                                />

                                <div className="flex justify-end space-x-4">
                                    <Button
                                        type="button"
                                        variant="secondary"
                                        onClick={() => navigate('/dashboard')}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={loading}
                                    >
                                        {loading ? 'Salvando...' : 'Salvar'}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Card>
                </div>
            </main>
        </div>
    );
}; 