import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button, Card } from '../components';
import { maintenanceService, droneService, Maintenance, Drone } from '../services';

export const MaintenanceDetails: React.FC = () => {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const [maintenance, setMaintenance] = useState<Maintenance | null>(null);
    const [drone, setDrone] = useState<Drone | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        if (id) {
            loadMaintenance(id);
        }
    }, [id]);

    const loadMaintenance = async (maintenanceId: string) => {
        try {
            const data = await maintenanceService.getById(maintenanceId);
            setMaintenance(data);
            await loadDrone(data.drone_id);
        } catch (err) {
            setError('Erro ao carregar dados da manutenção');
        } finally {
            setLoading(false);
        }
    };

    const loadDrone = async (droneId: string) => {
        try {
            const data = await droneService.getById(droneId);
            setDrone(data);
        } catch (err) {
            setError('Erro ao carregar dados do drone');
        }
    };

    const handleDelete = async () => {
        if (!maintenance || !window.confirm('Tem certeza que deseja excluir esta manutenção?')) {
            return;
        }

        try {
            await maintenanceService.delete(maintenance.id);
            navigate('/dashboard');
        } catch (err) {
            setError('Erro ao excluir manutenção');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <p>Carregando...</p>
            </div>
        );
    }

    if (!maintenance || !drone) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <p>Manutenção não encontrada</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            <nav className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="secondary"
                                onClick={() => navigate('/dashboard')}
                            >
                                Voltar
                            </Button>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="secondary"
                                onClick={() => navigate(`/maintenance/${maintenance.id}/edit`)}
                            >
                                Editar
                            </Button>
                            <Button
                                variant="danger"
                                onClick={handleDelete}
                            >
                                Excluir
                            </Button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    {error && (
                        <p className="text-red-600 mb-4">{error}</p>
                    )}

                    <Card className="mb-8">
                        <h2 className="text-2xl font-bold mb-4">
                            Manutenção #{maintenance.id}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-600">Status: {maintenance.status}</p>
                                <p className="text-gray-600">
                                    Data Agendada: {new Date(maintenance.scheduled_date).toLocaleDateString()}
                                </p>
                                {maintenance.completed_date && (
                                    <p className="text-gray-600">
                                        Data de Conclusão: {new Date(maintenance.completed_date).toLocaleDateString()}
                                    </p>
                                )}
                            </div>
                            <div>
                                <p className="text-gray-600">
                                    Data de Registro: {new Date(maintenance.created_at).toLocaleDateString()}
                                </p>
                                <p className="text-gray-600">
                                    Última Atualização: {new Date(maintenance.updated_at).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    </Card>

                    <Card className="mb-8">
                        <h3 className="text-xl font-bold mb-4">Descrição</h3>
                        <p className="text-gray-600 whitespace-pre-wrap">
                            {maintenance.description}
                        </p>
                    </Card>

                    <Card>
                        <h3 className="text-xl font-bold mb-4">Drone</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-600">Nome: {drone.name}</p>
                                <p className="text-gray-600">Modelo: {drone.model}</p>
                            </div>
                            <div>
                                <p className="text-gray-600">Status: {drone.status}</p>
                            </div>
                        </div>
                        <div className="mt-4">
                            <Button
                                variant="secondary"
                                onClick={() => navigate(`/drones/${drone.id}`)}
                            >
                                Ver Detalhes do Drone
                            </Button>
                        </div>
                    </Card>
                </div>
            </main>
        </div>
    );
}; 