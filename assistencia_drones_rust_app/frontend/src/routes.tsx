import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { Dashboard } from './pages/Dashboard';
import { DroneDetails } from './pages/DroneDetails';
import { DroneForm } from './pages/DroneForm';
import { MaintenanceDetails } from './pages/MaintenanceDetails';
import { MaintenanceForm } from './pages/MaintenanceForm';
import { authService } from './services';

const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const isAuthenticated = authService.isAuthenticated();
    return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

export const router = createBrowserRouter([
    {
        path: '/',
        element: <Navigate to="/dashboard" />
    },
    {
        path: '/login',
        element: <Login />
    },
    {
        path: '/register',
        element: <Register />
    },
    {
        path: '/dashboard',
        element: (
            <PrivateRoute>
                <Dashboard />
            </PrivateRoute>
        )
    },
    {
        path: '/drones/new',
        element: (
            <PrivateRoute>
                <DroneForm />
            </PrivateRoute>
        )
    },
    {
        path: '/drones/:id',
        element: (
            <PrivateRoute>
                <DroneDetails />
            </PrivateRoute>
        )
    },
    {
        path: '/drones/:id/edit',
        element: (
            <PrivateRoute>
                <DroneForm />
            </PrivateRoute>
        )
    },
    {
        path: '/maintenance/new',
        element: (
            <PrivateRoute>
                <MaintenanceForm />
            </PrivateRoute>
        )
    },
    {
        path: '/maintenance/:id',
        element: (
            <PrivateRoute>
                <MaintenanceDetails />
            </PrivateRoute>
        )
    },
    {
        path: '/maintenance/:id/edit',
        element: (
            <PrivateRoute>
                <MaintenanceForm />
            </PrivateRoute>
        )
    }
]); 