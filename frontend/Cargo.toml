[package]
name = "frontend"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
leptos = { version = "0.6", features = ["nightly"] }
leptos_meta = { version = "0.6", features = ["nightly"] }
leptos_router = { version = "0.6", features = ["nightly"] }
log = "0.4"
console_log = "1.0"
console_error_panic_hook = "0.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwasm = "0.2"
wasm-bindgen = "0.2"
web-sys = { version = "0.3", features = [
    "HtmlInputElement",
    "HtmlFormElement",
    "Window",
    "Document",
    "Element",
    "Event",
    "EventTarget",
    "MouseEvent",
    "SubmitEvent",
    "FormData",
    "console"
]}
gloo = "0.10"

[features]
default = ["csr"]
hydrate = ["leptos/hydrate", "leptos_meta/hydrate", "leptos_router/hydrate"]
csr = ["leptos/csr", "leptos_meta/csr", "leptos_router/csr"]
ssr = ["leptos/ssr", "leptos_meta/ssr", "leptos_router/ssr"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.release.package."*"]
opt-level = 3
lto = true
codegen-units = 1
