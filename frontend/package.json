{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:css": "tailwindcss -i ./src/frontend.css -o ./public/frontend.css", "watch:css": "tailwindcss -i ./src/frontend.css -o ./public/frontend.css --watch"}, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.4", "web-vitals": "^2.1.4"}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.3.1"}, "proxy": "http://localhost:8000"}