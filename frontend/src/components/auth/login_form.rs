use leptos::*;
use crate::services::auth::{login, LoginCredentials};

#[component]
pub fn LoginForm() -> impl IntoView {
    let (email, set_email) = create_signal(String::new());
    let (password, set_password) = create_signal(String::new());
    let (error, set_error) = create_signal(Option::<String>::None);
    let (is_loading, set_is_loading) = create_signal(false);

    let handle_submit = create_action(move |credentials: &LoginCredentials| {
        let credentials = credentials.clone();
        async move {
            set_is_loading.set(true);
            set_error.set(None);
            
            match login(credentials).await {
                Ok(response) => {
                    // Salvar o token no localStorage
                    if let Some(window) = web_sys::window() {
                        if let Some(local_storage) = window.local_storage().unwrap() {
                            let _ = local_storage.set_item("token", &response.token);
                        }
                    }
                    // Redirecionar para o dashboard
                    let window = web_sys::window().unwrap();
                    let _ = window.location().set_href("/dashboard");
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            set_is_loading.set(false);
        }
    });

    view! {
        <form
            on:submit=move |ev| {
                ev.prevent_default();
                handle_submit.dispatch(LoginCredentials {
                    email: email.get(),
                    password: password.get(),
                });
            }
            class="mt-8 space-y-6"
        >
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">"Email"</label>
                    <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                        placeholder="Email"
                        on:input=move |ev| {
                            set_email.set(event_target_value(&ev));
                        }
                    />
                </div>
                <div>
                    <label for="password" class="sr-only">"Senha"</label>
                    <input
                        id="password"
                        name="password"
                        type="password"
                        required
                        class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                        placeholder="Senha"
                        on:input=move |ev| {
                            set_password.set(event_target_value(&ev));
                        }
                    />
                </div>
            </div>

            <Show
                when=move || error.get().is_some()
                fallback=|| ()
            >
                <div class="text-red-500 text-sm">
                    {move || error.get().unwrap_or_default()}
                </div>
            </Show>

            <div>
                <button
                    type="submit"
                    disabled=is_loading
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                    {move || if is_loading.get() { "Carregando..." } else { "Entrar" }}
                </button>
            </div>
        </form>
    }
} 