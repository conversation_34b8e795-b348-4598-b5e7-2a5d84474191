use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use crate::pages::login::LoginPage;

mod components;
mod pages;
mod services;

#[component]
pub fn App() -> impl IntoView {
    // Fornece contexto para o título da página
    provide_meta_context();

    view! {
        <Stylesheet id="leptos" href="/pkg/frontend.css"/>
        <Title text="Sistema de Assistência de Drones"/>
        
        <Router>
            <Routes>
                <Route path="/" view=HomePage/>
                <Route path="/login" view=LoginPage/>
                <Route path="/register" view=RegisterPage/>
                <Route path="/dashboard" view=DashboardPage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100">
            <nav class="bg-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <div class="flex-shrink-0 flex items-center">
                                <h1 class="text-xl font-bold">"Sistema de Drones"</h1>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <A href="/login" class="text-gray-700 hover:text-gray-900 px-3 py-2">
                                "Login"
                            </A>
                            <A href="/register" class="text-gray-700 hover:text-gray-900 px-3 py-2">
                                "Registrar"
                            </A>
                        </div>
                    </div>
                </div>
            </nav>
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <div class="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
                        <h2 class="text-2xl font-semibold text-gray-700">
                            "Bem-vindo ao Sistema de Assistência de Drones"
                        </h2>
                    </div>
                </div>
            </main>
        </div>
    }
}

#[component]
fn LoginPage() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
                <div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        "Login"
                    </h2>
                </div>
                <form class="mt-8 space-y-6">
                    <div class="rounded-md shadow-sm -space-y-px">
                        <div>
                            <label for="email" class="sr-only">"Email"</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                placeholder="Email"
                            />
                        </div>
                        <div>
                            <label for="password" class="sr-only">"Senha"</label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                placeholder="Senha"
                            />
                        </div>
                    </div>
                    <div>
                        <button
                            type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            "Entrar"
                        </button>
                    </div>
                </form>
            </div>
        </div>
    }
}

#[component]
fn RegisterPage() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
                <div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        "Registro"
                    </h2>
                </div>
                <form class="mt-8 space-y-6">
                    <div class="rounded-md shadow-sm -space-y-px">
                        <div>
                            <label for="name" class="sr-only">"Nome"</label>
                            <input
                                id="name"
                                name="name"
                                type="text"
                                required
                                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                placeholder="Nome completo"
                            />
                        </div>
                        <div>
                            <label for="email" class="sr-only">"Email"</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                placeholder="Email"
                            />
                        </div>
                        <div>
                            <label for="password" class="sr-only">"Senha"</label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                placeholder="Senha"
                            />
                        </div>
                    </div>
                    <div>
                        <button
                            type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            "Registrar"
                        </button>
                    </div>
                </form>
            </div>
        </div>
    }
}

#[component]
fn DashboardPage() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100">
            <nav class="bg-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <div class="flex-shrink-0 flex items-center">
                                <h1 class="text-xl font-bold">"Dashboard"</h1>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <button class="text-gray-700 hover:text-gray-900 px-3 py-2">
                                "Sair"
                            </button>
                        </div>
                    </div>
                </div>
            </nav>
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    "Drones Ativos"
                                </h3>
                                <div class="mt-2 text-3xl font-semibold text-indigo-600">
                                    "0"
                                </div>
                            </div>
                        </div>
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    "Manutenções Pendentes"
                                </h3>
                                <div class="mt-2 text-3xl font-semibold text-red-600">
                                    "0"
                                </div>
                            </div>
                        </div>
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    "Total de Voos"
                                </h3>
                                <div class="mt-2 text-3xl font-semibold text-green-600">
                                    "0"
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    }
}

fn main() {
    _ = console_log::init_with_level(log::Level::Debug);
    console_error_panic_hook::set_once();
    
    mount_to_body(App);
} 