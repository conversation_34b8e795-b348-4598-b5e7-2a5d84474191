use serde::{Deserialize, Serialize};
use reqwasm::http::Request;
use wasm_bindgen_futures::spawn_local;
use web_sys::console;

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginCredentials {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterCredentials {
    pub name: String,
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub token: String,
    pub user: User,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct User {
    pub id: i32,
    pub name: String,
    pub email: String,
}

pub async fn login(credentials: LoginCredentials) -> Result<AuthResponse, String> {
    let response = Request::post("http://localhost:3000/auth/login")
        .header("Content-Type", "application/json")
        .body(serde_json::to_string(&credentials).unwrap())
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if response.status() == 200 {
        let auth_response: AuthResponse = response
            .json()
            .await
            .map_err(|e| e.to_string())?;
        Ok(auth_response)
    } else {
        Err("Credenciais inválidas".to_string())
    }
}

pub async fn register(credentials: RegisterCredentials) -> Result<AuthResponse, String> {
    let response = Request::post("http://localhost:3000/auth/register")
        .header("Content-Type", "application/json")
        .body(serde_json::to_string(&credentials).unwrap())
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if response.status() == 201 {
        let auth_response: AuthResponse = response
            .json()
            .await
            .map_err(|e| e.to_string())?;
        Ok(auth_response)
    } else {
        Err("Erro ao registrar usuário".to_string())
    }
}

pub fn logout() {
    // Limpar o token do localStorage
    if let Some(window) = web_sys::window() {
        if let Some(local_storage) = window.local_storage().unwrap() {
            let _ = local_storage.remove_item("token");
        }
    }
} 