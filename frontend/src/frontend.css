@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --error-color: #ef4444;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --text-color: #1f2937;
    --text-light: #6b7280;
    --bg-color: #f3f4f6;
    --white: #ffffff;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
    margin: 0;
    padding: 0;
}

.btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
}

.btn-primary {
    @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
}

.input {
    @apply appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm;
}

.card {
    @apply bg-white overflow-hidden shadow rounded-lg;
}

.card-header {
    @apply px-4 py-5 sm:px-6;
}

.card-body {
    @apply px-4 py-5 sm:p-6;
}

.card-footer {
    @apply px-4 py-4 sm:px-6 bg-gray-50;
} 