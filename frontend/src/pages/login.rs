use leptos::*;
use crate::components::auth::login_form::LoginForm;

#[component]
pub fn LoginPage() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
                <div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        "Login"
                    </h2>
                </div>
                <LoginForm/>
            </div>
        </div>
    }
} 