#!/bin/bash

# Função para executar comandos com feedback
echo_and_run() {
    echo "\n=== Executando: $1 ==="
    eval "$1"
}

# Configurações básicas
echo "Iniciando configuração completa do projeto..."
export PROJECT_DIR="/root/CascadeProjects/windsurf-project/assistencia_drones_rust_app"

# 1. Configuração Inicial
echo "\n=== Configuração Inicial ==="

echo_and_run "mkdir -p $PROJECT_DIR/{backend,frontend,shared,docker,.github}"

cd $PROJECT_DIR

# 2. Configuração do Backend
echo "\n=== Configurando Backend ==="

cd backend
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "axum = \"^0.7\"" >> Cargo.toml
echo "tokio = { version = \"^1.32\", features = [\"full\"] }" >> Cargo.toml
echo "sqlx = { version = \"^0.7\", features = [\"postgres\", \"runtime-tokio-native-tls\"] }" >> Cargo.toml
echo "validator = \"^0.16\"" >> Cargo.toml
echo "tracing = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml
echo "uuid = { version = \"^1.4\", features = [\"serde\", \"v4\"] }" >> Cargo.toml
echo "dotenv = \"^0.15\"" >> Cargo.toml

echo_and_run "mkdir -p src/{models,handlers,routes,services}"

echo "// src/main.rs" > src/main.rs
echo "use axum::{" >> src/main.rs
echo "    routing::get," >> src/main.rs
echo "    Router," >> src/main.rs
echo "};" >> src/main.rs
echo "use dotenv::dotenv;" >> src/main.rs
echo "use std::env;" >> src/main.rs
echo "use tracing_subscriber;" >> src/main.rs
echo "use crate::routes::auth::auth_routes;" >> src/main.rs
echo "use crate::routes::drones::drones_routes;" >> src/main.rs
echo "use crate::routes::maintenance::maintenance_routes;" >> src/main.rs
echo "use crate::services::db::init_db;" >> src/main.rs
echo "" >> src/main.rs
echo "#[tokio::main]" >> src/main.rs
echo "async fn main() {" >> src/main.rs
echo "    dotenv().ok();" >> src/main.rs
echo "    tracing_subscriber::fmt::init();" >> src/main.rs
echo "    " >> src/main.rs
echo "    let db_pool = init_db().await;" >> src/main.rs
echo "    " >> src/main.rs
echo "    let app = Router::new()" >> src/main.rs
echo "        .merge(auth_routes())" >> src/main.rs
echo "        .merge(drones_routes())" >> src/main.rs
echo "        .merge(maintenance_routes());" >> src/main.rs
echo "    " >> src/main.rs
echo "    axum::Server::bind(&\"0.0.0.0:3000\".parse().unwrap())" >> src/main.rs
echo "        .serve(app.into_make_service())" >> src/main.rs
echo "        .await" >> src/main.rs
echo "        .unwrap();" >> src/main.rs
echo "}" >> src/main.rs

# 3. Configuração do Frontend
echo "\n=== Configurando Frontend ==="

cd ../frontend
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "leptos = \"^0.4\"" >> Cargo.toml
echo "reqwasm = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml

echo_and_run "mkdir -p src/{components,pages,services}"

echo "// src/main.rs" > src/main.rs
echo "use leptos::*;" >> src/main.rs
echo "use leptos_router::*;" >> src/main.rs
echo "use crate::app::App;" >> src/main.rs
echo "" >> src/main.rs
echo "#[component]" >> src/main.rs
echo "fn main() -> impl IntoView {" >> src/main.rs
echo "    leptos::mount_to_body(|cx| view! { cx, <App /> })" >> src/main.rs
echo "}" >> src/main.rs

# 4. Configuração do Shared
echo "\n=== Configurando Shared ==="

cd ../shared
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml

# 5. Configuração do Docker
echo "\n=== Configurando Docker ==="

cd ../docker

echo "version: '3.8'" > docker-compose.yml
echo "services:" >> docker-compose.yml
echo "  postgres:" >> docker-compose.yml
echo "    image: postgres:15" >> docker-compose.yml
echo "    environment:" >> docker-compose.yml
echo "      POSTGRES_DB: drones_db" >> docker-compose.yml
echo "      POSTGRES_USER: admin" >> docker-compose.yml
echo "      POSTGRES_PASSWORD: admin" >> docker-compose.yml
echo "    ports:" >> docker-compose.yml
echo "      - \"5432:5432\"" >> docker-compose.yml
echo "    volumes:" >> docker-compose.yml
echo "      - postgres_data:/var/lib/postgresql/data" >> docker-compose.yml
echo "" >> docker-compose.yml
echo "volumes:" >> docker-compose.yml
echo "  postgres_data:" >> docker-compose.yml

# 6. Configuração do Workspace
echo "\n=== Configurando Workspace ==="

cd ..
echo "[workspace]" > Cargo.toml
echo "members = [" >> Cargo.toml
echo "    \"backend\"," >> Cargo.toml
echo "    \"frontend\"," >> Cargo.toml
echo "    \"shared\"" >> Cargo.toml
echo "]" >> Cargo.toml

# 7. Configuração do Git
echo "\n=== Configurando Git ==="

echo_and_run "git init"

echo ".env" > .gitignore
echo "target/" >> .gitignore
echo "**/*.rs.bk" >> .gitignore
echo "**/*.rs~" >> .gitignore

echo "DATABASE_URL=postgres://admin:admin@localhost:5432/drones_db" > .env.example
echo "RUST_LOG=info" >> .env.example

# 8. Adicionando arquivos ao Git
echo_and_run "git add ."
echo_and_run "git commit -m \"Initial project setup\""

echo "\n=== Configuração concluída com sucesso! ===\n"
echo "Para iniciar o desenvolvimento:" >> README.md
echo "1. Configure as variáveis de ambiente: cp .env.example .env" >> README.md
echo "2. Inicie o banco de dados: docker-compose up -d" >> README.md
echo "3. Compile e execute o projeto: cargo run" >> README.md

echo "\nProjeto configurado com sucesso!"
