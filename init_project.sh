#!/bin/bash

# Configurações básicas
echo "Configurando ambiente..."
export PROJECT_DIR="/root/CascadeProjects/windsurf-project/assistencia_drones_rust_app"
export CARGO_HOME="$HOME/.cargo"

# Criar estrutura básica do projeto
mkdir -p "$PROJECT_DIR"/{backend,frontend,shared,.github}

# Inicializar workspace Rust
cd "$PROJECT_DIR"
echo "[workspace]" > Cargo.toml
echo "members = [" >> Cargo.toml
echo "    \"backend\"," >> Cargo.toml
echo "    \"frontend\"," >> Cargo.toml
echo "    \"shared\"" >> Cargo.toml
echo "]" >> Cargo.toml

# Configurar backend
cd "$PROJECT_DIR/backend"
cargo init --lib
echo "[dependencies]" > Cargo.toml
echo "axum = \"^0.7\"" >> Cargo.toml
echo "tokio = { version = \"^1.32\", features = [\"full\"] }" >> Cargo.toml
echo "sqlx = { version = \"^0.7\", features = [\"postgres\", \"runtime-tokio-native-tls\"] }" >> Cargo.toml
echo "validator = \"^0.16\"" >> Cargo.toml
echo "tracing = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml
echo "uuid = { version = \"^1.4\", features = [\"serde", \"v4\"] }" >> Cargo.toml
echo "dotenv = \"^0.15\"" >> Cargo.toml

# Configurar frontend
cd "$PROJECT_DIR/frontend"
cargo init --lib
echo "[dependencies]" > Cargo.toml
echo "leptos = \"^0.4\"" >> Cargo.toml
echo "reqwasm = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml

# Configurar shared
cd "$PROJECT_DIR/shared"
cargo init --lib
echo "[dependencies]" > Cargo.toml

# Configurar docker-compose
mkdir -p "$PROJECT_DIR/docker"
cd "$PROJECT_DIR/docker"

echo "version: '3.8'" > docker-compose.yml
echo "services:" >> docker-compose.yml
echo "  postgres:" >> docker-compose.yml
echo "    image: postgres:15" >> docker-compose.yml
echo "    environment:" >> docker-compose.yml
echo "      POSTGRES_DB: drones_db" >> docker-compose.yml
echo "      POSTGRES_USER: admin" >> docker-compose.yml
echo "      POSTGRES_PASSWORD: admin" >> docker-compose.yml
echo "    ports:" >> docker-compose.yml
echo "      - \"5432:5432\"" >> docker-compose.yml
echo "    volumes:" >> docker-compose.yml
echo "      - postgres_data:/var/lib/postgresql/data" >> docker-compose.yml
echo "" >> docker-compose.yml
echo "volumes:" >> docker-compose.yml
echo "  postgres_data:" >> docker-compose.yml

# Configurar variáveis de ambiente
cd "$PROJECT_DIR"
echo "DATABASE_URL=postgres://admin:admin@localhost:5432/drones_db" > .env.example
echo "RUST_LOG=info" >> .env.example

# Inicializar git
git init

# Criar arquivo de tarefas
echo "[project]" > TASKS_CONFIG.toml
echo "name = \"assistencia_drones_rust_app\"" >> TASKS_CONFIG.toml
echo "version = \"0.1.0\"" >> TASKS_CONFIG.toml

echo "[task_groups.init]" >> TASKS_CONFIG.toml
echo "description = \"Configuração inicial do ambiente\"" >> TASKS_CONFIG.toml
echo "dependencies = []" >> TASKS_CONFIG.toml
echo "tasks = [\"setup_workspace\", \"config_db\", \"setup_frontend\"]" >> TASKS_CONFIG.toml

echo "[task_groups.backend]" >> TASKS_CONFIG.toml
echo "description = \"Configuração do backend\"" >> TASKS_CONFIG.toml
echo "dependencies = [\"init\"]" >> TASKS_CONFIG.toml
echo "tasks = [\"auth_setup\", \"drones_crud\", \"maintenance\"]" >> TASKS_CONFIG.toml

echo "[task_groups.frontend]" >> TASKS_CONFIG.toml
echo "description = \"Configuração do frontend\"" >> TASKS_CONFIG.toml
echo "dependencies = [\"backend\"]" >> TASKS_CONFIG.toml
echo "tasks = [\"auth_pages\", \"drones_management\", \"maintenance_ui\"]" >> TASKS_CONFIG.toml

echo "[tasks.setup_workspace]" >> TASKS_CONFIG.toml
echo "description = \"Configuração inicial do workspace Rust\"" >> TASKS_CONFIG.toml
echo "command = \"setup_workspace\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.config_db]" >> TASKS_CONFIG.toml
echo "description = \"Configuração do banco de dados\"" >> TASKS_CONFIG.toml
echo "command = \"config_db\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.setup_frontend]" >> TASKS_CONFIG.toml
echo "description = \"Configuração inicial do frontend\"" >> TASKS_CONFIG.toml
echo "command = \"setup_frontend\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.auth_setup]" >> TASKS_CONFIG.toml
echo "description = \"Configuração de autenticação\"" >> TASKS_CONFIG.toml
echo "command = \"auth_setup\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.drones_crud]" >> TASKS_CONFIG.toml
echo "description = \"Implementação do CRUD de drones\"" >> TASKS_CONFIG.toml
echo "command = \"drones_crud\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.maintenance]" >> TASKS_CONFIG.toml
echo "description = \"Implementação de manutenção\"" >> TASKS_CONFIG.toml
echo "command = \"maintenance\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.auth_pages]" >> TASKS_CONFIG.toml
echo "description = \"Páginas de autenticação\"" >> TASKS_CONFIG.toml
echo "command = \"auth_pages\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.drones_management]" >> TASKS_CONFIG.toml
echo "description = \"Interface de gerenciamento de drones\"" >> TASKS_CONFIG.toml
echo "command = \"drones_management\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

echo "[tasks.maintenance_ui]" >> TASKS_CONFIG.toml
echo "description = \"Interface de manutenção\"" >> TASKS_CONFIG.toml
echo "command = \"maintenance_ui\"" >> TASKS_CONFIG.toml
echo "status = \"pending\"" >> TASKS_CONFIG.toml

# Criar arquivo README.md
README_CONTENT="# Assistência Técnica de Drones

Aplicação web para gerenciamento de drones e solicitações de manutenção.

## Tecnologias

- Backend: Rust com Axum
- Frontend: Rust com Leptos (WebAssembly)
- Banco de Dados: PostgreSQL
- ORM: SQLx
- UI: Tailwind CSS

## Instalação

1. Clone o repositório
2. Configure as variáveis de ambiente (veja .env.example)
3. Inicie os serviços com docker-compose:
   ```bash
docker-compose up -d
```
4. Compile e execute o projeto:
   ```bash
cargo run
```

## Estrutura do Projeto

```
assistencia_drones_rust_app/
├── backend/        # Backend com Axum
├── frontend/       # Frontend com Leptos
├── shared/         # Código compartilhado
├── docker/         # Configurações Docker
└── .github/       # Workflows GitHub
```

## Desenvolvimento

- Backend: http://localhost:3000
- Frontend: http://localhost:8080
- Banco de Dados: postgres://localhost:5432/drones_db

## Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Faça commit das suas mudanças
4. Abra um Pull Request

## Licença

MIT License"

echo "$README_CONTENT" > README.md

# Adicionar arquivos ao git
git add .
git commit -m "Initial project setup"

echo "Configuração inicial concluída!"
