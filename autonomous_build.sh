#!/bin/bash

# Função para executar comandos com feedback
echo_and_run() {
    echo "\n=== Executando: $1 ==="
    eval "$1"
}

# Configurações básicas
echo "Iniciando configuração completa do projeto..."
export PROJECT_DIR="/root/CascadeProjects/windsurf-project/assistencia_drones_rust_app"

# 1. Configuração Inicial
echo "\n=== Configuração Inicial ==="

echo_and_run "mkdir -p $PROJECT_DIR/{backend,frontend,shared,docker,.github}"

cd $PROJECT_DIR

# 2. Configuração do Backend
echo "\n=== Configurando Backend ==="

cd backend
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "axum = \"^0.7\"" >> Cargo.toml
echo "tokio = { version = \"^1.32\", features = [\"full\"] }" >> Cargo.toml
echo "sqlx = { version = \"^0.7\", features = [\"postgres\", \"runtime-tokio-native-tls\"] }" >> Cargo.toml
echo "validator = \"^0.16\"" >> Cargo.toml
echo "tracing = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml
echo "uuid = { version = \"^1.4\", features = [\"serde\", \"v4\"] }" >> Cargo.toml
echo "dotenv = \"^0.15\"" >> Cargo.toml

echo_and_run "mkdir -p src/{models,handlers,routes,services}"

# Implementação do Backend
echo_and_run "touch src/models/{user,drone,maintenance}.rs"
echo_and_run "touch src/services/{auth,drone,maintenance}.rs"
echo_and_run "touch src/routes/{auth,drones,maintenance}.rs"

# 3. Configuração do Frontend
echo "\n=== Configurando Frontend ==="

cd ../frontend
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "leptos = \"^0.4\"" >> Cargo.toml
echo "reqwasm = \"^0.1\"" >> Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml
echo "serde_json = \"^1.0\"" >> Cargo.toml

echo_and_run "mkdir -p src/{components,pages,services}"

# Implementação do Frontend
echo_and_run "touch src/components/{auth,drones,maintenance}.rs"
echo_and_run "touch src/pages/{login,register,dashboard}.rs"

# 4. Configuração do Shared
echo "\n=== Configurando Shared ==="

cd ../shared
echo_and_run "cargo init --lib"

echo "[dependencies]" > Cargo.toml
echo "serde = { version = \"^1.0\", features = [\"derive\"] }" >> Cargo.toml

echo_and_run "mkdir -p src"

# 5. Configuração do Docker
echo "\n=== Configurando Docker ==="

cd ../docker

echo "version: '3.8'" > docker-compose.yml
echo "services:" >> docker-compose.yml
echo "  postgres:" >> docker-compose.yml
echo "    image: postgres:15" >> docker-compose.yml
echo "    environment:" >> docker-compose.yml
echo "      POSTGRES_DB: drones_db" >> docker-compose.yml
echo "      POSTGRES_USER: admin" >> docker-compose.yml
echo "      POSTGRES_PASSWORD: admin" >> docker-compose.yml
echo "    ports:" >> docker-compose.yml
echo "      - \"5432:5432\"" >> docker-compose.yml
echo "    volumes:" >> docker-compose.yml
echo "      - postgres_data:/var/lib/postgresql/data" >> docker-compose.yml
echo "" >> docker-compose.yml
echo "volumes:" >> docker-compose.yml
echo "  postgres_data:" >> docker-compose.yml

# 6. Configuração do Workspace
echo "\n=== Configurando Workspace ==="

cd ..
echo "[workspace]" > Cargo.toml
echo "members = [" >> Cargo.toml
echo "    \"backend\"," >> Cargo.toml
echo "    \"frontend\"," >> Cargo.toml
echo "    \"shared\"" >> Cargo.toml
echo "]" >> Cargo.toml

# 7. Configuração do Git
echo "\n=== Configurando Git ==="

echo_and_run "git init"

echo ".env" > .gitignore
echo "target/" >> .gitignore
echo "**/*.rs.bk" >> .gitignore
echo "**/*.rs~" >> .gitignore

echo "DATABASE_URL=postgres://admin:admin@localhost:5432/drones_db" > .env.example
echo "RUST_LOG=info" >> .env.example

# 8. Adicionando arquivos ao Git
echo_and_run "git add ."
echo_and_run "git commit -m \"Initial project setup\""

# 9. Iniciando o banco de dados
echo "\n=== Iniciando banco de dados ==="
echo_and_run "cp .env.example .env"
echo_and_run "docker-compose up -d"

# 10. Compilando e executando o projeto
echo "\n=== Compilando e executando o projeto ==="
echo_and_run "cargo run"

echo "\n=== Configuração concluída com sucesso! ===\n"
echo "Projeto configurado e em execução!"
