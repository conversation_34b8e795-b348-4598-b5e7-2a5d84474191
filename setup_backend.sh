#!/bin/bash

# Criar diretórios necessários
mkdir -p backend/migrations

# Criar arquivo de migração
cat > backend/migrations/20250101000000_initial_schema.sql << 'EOL'
-- Criação da tabela de usuários
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Criação da tabela de drones
CREATE TABLE IF NOT EXISTS drones (
    id SERIAL PRIMARY KEY,
    model VARCHAR(100) NOT NULL,
    manufacturer VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100) UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Criação da tabela de manutenções
CREATE TABLE IF NOT EXISTS maintenance (
    id SERIAL PRIMARY KEY,
    drone_id INTEGER NOT NULL REFERENCES drones(id) ON DELETE CASCADE,
    reported_by INTEGER NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Índices para melhorar performance
CREATE INDEX idx_drone_status ON drones(status);
CREATE INDEX idx_maintenance_drone_id ON maintenance(drone_id);
CREATE INDEX idx_maintenance_status ON maintenance(status);

-- Gatilho para atualizar o updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar o gatilho em todas as tabelas
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drones_updated_at
BEFORE UPDATE ON drones
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_updated_at
BEFORE UPDATE ON maintenance
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EOL

echo "✅ Estrutura do backend configurada com sucesso!"
echo "Próximos passos:"
echo "1. Configure o banco de dados PostgreSQL"
echo "2. Execute as migrações com: sqlx migrate run"
echo "3. Inicie o servidor com: cargo run"
