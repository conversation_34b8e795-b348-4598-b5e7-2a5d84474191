# Backend - Sistema de Assistência de Drones

Backend desenvolvido em Rust com Axum para o sistema de gerenciamento de assistência técnica de drones.

## 🚀 Começando

### Pré-requisitos

- Rust (última versão estável)
- PostgreSQL (versão 13 ou superior)
- Cargo (gerenciador de pacotes do Rust)

### Configuração do Ambiente

1. **Configurar as variáveis de ambiente**
   - Faça uma cópia do arquivo `.env.example` para `.env`
   - Configure as variáveis de acordo com o seu ambiente

2. **Configurar o banco de dados**
   - Crie um banco de dados PostgreSQL
   - Atualize a string de conexão no arquivo `.env`

### Instalação

```bash
# Clonar o repositório
git clone https://github.com/seu-usuario/assistencia-drones.git
cd assistencia-drones/backend

# Instalar dependências
cargo build
```

## 🛠 Executando o Projeto

### Desenvolvimento

```bash
# Iniciar o servidor de desenvolvimento
cargo run
```

O servidor estará disponível em: `http://localhost:3000`

### Migrações

```bash
# Executar migrações
cargo install sqlx-cli
sqlx migrate run
```

## 📚 Documentação da API

A documentação da API está disponível em `/docs` quando o servidor estiver em execução.

## 🧪 Testes

```bash
# Executar todos os testes
cargo test

# Executar testes com cobertura
cargo tarpaulin --out Html
```

## 🏗 Estrutura do Projeto

```
backend/
├── src/
│   ├── config/       # Configurações da aplicação
│   ├── handlers/     # Handlers das rotas
│   ├── middleware/   # Middlewares globais
│   ├── models/       # Modelos de dados
│   ├── routes/       # Definição das rotas
│   ├── error.rs      # Tratamento de erros
│   └── main.rs       # Ponto de entrada da aplicação
├── migrations/       # Migrações do banco de dados
├── tests/            # Testes de integração
├── .env.example      # Exemplo de variáveis de ambiente
└── Cargo.toml        # Dependências e metadados
```

## 🔒 Segurança

- Todas as rotas, exceto as públicas, requerem autenticação via JWT
- As senhas são armazenadas usando bcrypt
- Headers de segurança HTTP estão habilitados
- CORS está configurado para aceitar requisições apenas do domínio permitido

## 📝 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🤝 Contribuição

1. Faça um Fork do projeto
2. Crie uma Branch para sua Feature (`git checkout -b feature/AmazingFeature`)
3. Adicione suas mudanças (`git add .`)
4. Comite suas alterações (`git commit -m 'Add some AmazingFeature'`)
5. Faça o Push da Branch (`git push origin feature/AmazingFeature`)
6. Abra um Pull Request
