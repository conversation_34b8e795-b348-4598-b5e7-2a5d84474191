use axum::{
    async_trait,
    extract::{FromRequestParts, State},
    http::{request::Parts, StatusCode},
    RequestPartsExt,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use serde::{Deserialize, Serialize};

use crate::{config::Config, error::AppError};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub exp: i64,
}

#[async_trait]
impl<S> FromRequestParts<S> for Claims
where
    S: Send + Sync,
{
    type Rejection = AppError;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // Extrair o token do cabeçalho Authorization
        let auth_header = parts
            .headers
            .get("Authorization")
            .and_then(|header| header.to_str().ok())
            .ok_or(AppError::Unauthorized)?;

        if !auth_header.starts_with("Bearer ") {
            return Err(AppError::Unauthorized);
        }

        let token = &auth_header[7..]; // Remove 'Bearer ' do início
        
        // Obter a configuração do estado
        let State(config) = parts
            .extract::<State<Config>>()
            .await
            .map_err(|_| AppError::InternalServerError)?;

        // Decodificar e validar o token
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(config.jwt_secret.as_ref()),
            &Validation::default(),
        )
        .map_err(|_| AppError::Unauthorized)?;

        // Verificar se o token expirou
        let now = chrono::Utc::now().timestamp();
        if token_data.claims.exp < now {
            return Err(AppError::Unauthorized);
        }

        Ok(token_data.claims)
    }
}

/// Middleware para autenticação JWT
pub async fn auth_middleware<B>(
    req: axum::http::Request<B>,
    next: axum::middleware::Next<B>,
) -> Result<axum::response::Response, AppError> {
    // Extrair as partes da requisição
    let (mut parts, body) = req.into_parts();
    
    // Extrair o token do cabeçalho Authorization
    let auth_header = parts
        .headers
        .get("Authorization")
        .and_then(|header| header.to_str().ok())
        .ok_or(AppError::Unauthorized)?;

    if !auth_header.starts_with("Bearer ") {
        return Err(AppError::Unauthorized);
    }

    let token = &auth_header[7..]; // Remove 'Bearer ' do início
    
    // Obter a configuração do estado
    let State(config) = parts
        .extract::<State<Config>>()
        .await
        .map_err(|_| AppError::InternalServerError)?;

    // Decodificar e validar o token
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(config.jwt_secret.as_ref()),
        &Validation::default(),
    )
    .map_err(|_| AppError::Unauthorized)?;

    // Verificar se o token expirou
    let now = chrono::Utc::now().timestamp();
    if token_data.claims.exp < now {
        return Err(AppError::Unauthorized);
    }

    // Adicionar os claims à extensão da requisição para uso posterior
    parts.extensions.insert(token_data.claims);
    
    // Reconstruir a requisição e continuar
    let req = axum::http::Request::from_parts(parts, body);
    Ok(next.run(req).await)
}
