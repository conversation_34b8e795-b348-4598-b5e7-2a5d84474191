use axum::{
    routing::{get, post},
    Router,
    http::{
        header::{ACCEPT, AUTHORIZATION, CONTENT_TYPE},
        HeaderValue, Method, StatusCode,
    },
    response::IntoResponse,
    Json,
    extract::Extension,
    middleware,
};
use std::{net::SocketAddr, sync::Arc};
use tower_http::{
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};
use sqlx::postgres::PgPoolOptions;

mod config;
mod error;
mod handlers;
mod middleware;
mod models;
mod routes;

use crate::{config::Config, error::AppError};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Carregar variáveis de ambiente
    dotenv::dotenv().ok();
    
    // Inicializar logging
    tracing_subscriber::registry()
        .with(EnvFilter::from_default_env()
            .add_directive("assistencia_drones_backend=debug".parse()?)
            .add_directive("tower_http=debug".parse()?))
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Carregar configuração
    let config = Config::from_env().expect("Falha ao carregar configuração");
    
    // Conectar ao banco de dados
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&config.database_url)
        .await
        .expect("Falha ao conectar ao banco de dados");

    // Executar migrações
    sqlx::migrate!("./migrations")
        .run(&pool)
        .await
        .expect("Falha ao executar migrações");

    // Configurar CORS
    let cors = CorsLayer::new()
        .allow_origin("http://localhost:3000".parse::<HeaderValue>().unwrap())
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers([AUTHORIZATION, ACCEPT, CONTENT_TYPE])
        .allow_credentials(true);

    // Criar roteador da aplicação
    let app = Router::new()
    .layer(create_cors_layer())
        // Rotas públicas
        .merge(routes::health::health_router())
        .merge(routes::auth::auth_router())
        
        // Rotas protegidas
        .route("/api/protected", get(protected_route))
        
        // Camadas de middleware
        .layer(cors)
        .layer(TraceLayer::new_for_http())
        .layer(Extension(Arc::new(config)))
        .layer(Extension(pool))
        .layer(middleware::from_fn(middleware::auth::auth_middleware));

    // Iniciar servidor
    let addr = SocketAddr::from(([0, 0, 0, 0], 3000)); // Usando porta fixa para evitar conflitos
    tracing::info!("🚀 Servidor rodando em http://{}", addr);
    
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await?;

    Ok(())
}

// Rota de exemplo protegida
async fn protected_route(
    claims: middleware::auth::Claims,
) -> Result<impl IntoResponse, AppError> {
    Ok(Json(serde_json::json!({
        "message": "Rota protegida - Acesso autorizado",
        "user_id": claims.sub,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// Handler para erros 404
async fn handle_404() -> impl IntoResponse {
    (
        StatusCode::NOT_FOUND,
        Json(serde_json::json!({ 
            "error": "Rota não encontrada",
            "code": 404,
            "timestamp": chrono::Utc::now().to_rfc3339()
        })),
    )
}
