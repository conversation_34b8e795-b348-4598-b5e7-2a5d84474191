use axum::{
    Extension,
    Json,
    routing::post,
    Router,
};
use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::{
    config::Config,
    handlers::auth,
};

#[derive(Debug, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(email)]
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub token: String,
    pub user: UserResponse,
}

#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: i32,
    pub email: String,
    pub name: String,
}

pub fn auth_router(config: &Config) -> Router {
    Router::new()
        .route("/auth/register", post(auth::register))
        .route("/auth/login", post(auth::login))
        .layer(Extension(config.clone()))
}
