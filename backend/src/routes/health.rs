use axum::{
    http::StatusCode,
    response::IntoResponse,
    routing::get,
    Json,
    Router,
};
use serde_json::json;

#[derive(serde::Serialize)]
struct HealthCheck {
    status: String,
    version: &'static str,
}

pub fn health_router() -> Router {
    Router::new().route("/health", get(health_check))
}

async fn health_check() -> impl IntoResponse {
    let health = HealthCheck {
        status: "ok".to_string(),
        version: env!("CARGO_PKG_VERSION"),
    };
    
    (StatusCode::OK, <PERSON><PERSON>(json!(health)))
}
