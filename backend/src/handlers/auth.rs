use axum::{
    <PERSON>,
    Json,
    response::IntoResponse,
};
use bcrypt::{hash, verify, DEFAULT_COST};
use jsonwebtoken::{encode, Encoding<PERSON><PERSON>, Header};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use validator::Validate;

use crate::{
    config::Config,
    models::user::{NewUser, User},
    routes::auth::{AuthResponse, LoginRequest, UserResponse},
    AppError,
};

const JWT_EXPIRATION: i64 = 60 * 60 * 24 * 7; // 1 week

pub async fn register(
    Extension(pool): Extension<PgPool>,
    Json(payload): Json<NewUser>,
) -> Result<impl IntoResponse, AppError> {
    // Validação de entrada
    payload.validate()?;

    // Verifica se o usuário já existe
    let user_exists = sqlx::query!(
        "SELECT id FROM users WHERE email = $1",
        payload.email
    )
    .fetch_optional(&pool)
    .await?;

    if user_exists.is_some() {
        return Err(AppError::UserAlreadyExists);
    }

    // Hash da senha
    let hashed_password = hash(payload.password, DEFAULT_COST)?;

    // Cria o usuário
    let user = sqlx::query_as!(
        User,
        "INSERT INTO users (email, name, password) VALUES ($1, $2, $3) RETURNING *",
        payload.email,
        payload.name,
        hashed_password
    )
    .fetch_one(&pool)
    .await?;

    // Gera o token JWT
    let token = generate_jwt(user.id, &config.jwt_secret)?;

    // Prepara a resposta
    let response = AuthResponse {
        token,
        user: UserResponse {
            id: user.id,
            email: user.email,
            name: user.name,
        },
    };

    Ok(Json(json!(response)))
}

pub async fn login(
    Extension(pool): Extension<PgPool>,
    Extension(config): Extension<Config>,
    Json(payload): Json<LoginRequest>,
) -> Result<impl IntoResponse, AppError> {
    // Validação de entrada
    payload.validate()?;

    // Busca o usuário
    let user = sqlx::query_as!(
        User,
        "SELECT * FROM users WHERE email = $1",
        payload.email
    )
    .fetch_optional(&pool)
    .await?
    .ok_or(AppError::InvalidCredentials)?;

    // Verifica a senha
    if !verify(&payload.password, &user.password)? {
        return Err(AppError::InvalidCredentials);
    }

    // Gera o token JWT
    let token = generate_jwt(user.id, &config.jwt_secret)?;

    // Prepara a resposta
    let response = AuthResponse {
        token,
        user: UserResponse {
            id: user.id,
            email: user.email,
            name: user.name,
        },
    };

    Ok(Json(json!(response)))
}

fn generate_jwt(user_id: i32, secret: &str) -> Result<String, AppError> {
    let expiration = chrono::Utc::now()
        .checked_add_signed(chrono::Duration::seconds(JWT_EXPIRATION))
        .expect("timestamp válido")
        .timestamp();

    let claims = json!({
        "sub": user_id.to_string(),
        "exp": expiration,
    });

    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )?;

    Ok(token)
}
