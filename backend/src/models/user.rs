use serde::{Deserialize, Serialize};
use validator::Valida<PERSON>;

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, sqlx::FromRow)]
pub struct User {
    pub id: i32,
    pub email: String,
    pub name: String,
    pub password: String,
    pub created_at: chrono::NaiveDateTime,
    pub updated_at: chrono::NaiveDateTime,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct NewUser {
    #[validate(email, length(min = 5, max = 255))]
    pub email: String,
    
    #[validate(length(min = 3, max = 100))]
    pub name: String,
    
    #[validate(length(min = 8, max = 100))]
    pub password: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(email)]
    pub email: String,
    
    #[validate(length(min = 8))]
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: i32,
    pub email: String,
    pub name: String,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            email: user.email,
            name: user.name,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub token: String,
    pub user: UserResponse,
}
