use std::env;
use dotenv::dotenv;
use serde::Deserialize;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Config {
    pub database_url: String,
    pub jwt_secret: String,
    pub port: u16,
    pub env: Environment,
}

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum Environment {
    Development,
    Production,
}

impl Config {
    pub fn from_env() -> Result<Self, env::VarError> {
        dotenv().ok();

        let database_url = env::var("DATABASE_URL")?;
        let jwt_secret = env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret".to_string());
        let port = env::var("PORT")
            .unwrap_or_else(|_| "3000".to_string())
            .parse()
            .unwrap_or(3000);

        let env = match env::var("ENV").unwrap_or_else(|_| "development".to_string()).as_str() {
            "production" => Environment::Production,
            _ => Environment::Development,
        };

        Ok(Self {
            database_url,
            jwt_secret,
            port,
            env,
        })
    }

    pub fn is_development(&self) -> bool {
        self.env == Environment::Development
    }

    pub fn is_production(&self) -> bool {
        self.env == Environment::Production
    }
}
