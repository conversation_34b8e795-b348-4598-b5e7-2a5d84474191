[package]
name = "assistencia-drones-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Framework Web
axum = { version = "0.7", features = ["headers", "json"] }
tokio = { version = "1.32", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Banco de Dados
sqlx = { version = "0.7", features = ["postgres", "runtime-tokio-native-tls", "chrono", "uuid"] }

# Autenticação e Segurança
jsonwebtoken = "9.0"
bcrypt = "0.15"

# Validação
validator = { version = "0.16", features = ["derive"] }

# Serialização/Desserialização
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Utilitários
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
dotenv = "0.15"

# Módulos compartilhados
assistencia-drones-shared = { path = "../shared" }
